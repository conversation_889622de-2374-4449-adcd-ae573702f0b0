<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发安全赋能平台 - 三叉戟</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        
        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }
        
        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .module-card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15);
            border-color: #409eff;
        }
        
        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .module-icon {
            font-size: 24px;
            margin-right: 12px;
            color: #409eff;
        }
        
        .module-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }
        
        .module-description {
            color: #606266;
            line-height: 1.6;
            margin-bottom: 16px;
        }
        
        .module-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .module-features li {
            padding: 4px 0;
            color: #909399;
            font-size: 14px;
        }
        
        .module-features li:before {
            content: "•";
            color: #67c23a;
            margin-right: 8px;
        }
        
        .breadcrumb {
            margin-bottom: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 三叉戟
                    </div>
                    <div class="user-info">
                        <el-dropdown>
                            <span class="el-dropdown-link">
                                <el-avatar size="small" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
                                <span style="margin-left: 8px;">安全专家</span>
                                <el-icon><arrow-down /></el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>个人中心</el-dropdown-item>
                                    <el-dropdown-item>系统设置</el-dropdown-item>
                                    <el-dropdown-item divided>退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>
            
            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu 
                        :default-active="activeMenu" 
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="threat-modeling">
                            <el-icon><warning /></el-icon>
                            <span>威胁建模</span>
                        </el-menu-item>
                        <el-menu-item index="security-requirements">
                            <el-icon><document /></el-icon>
                            <span>安全需求库</span>
                        </el-menu-item>
                        <el-menu-item index="asoc-testing">
                            <el-icon><cpu /></el-icon>
                            <span>安全测试编排</span>
                        </el-menu-item>
                        <el-menu-item index="data-security">
                            <el-icon><lock /></el-icon>
                            <span>数据安全评估</span>
                        </el-menu-item>
                        <el-menu-item index="attack-surface">
                            <el-icon><monitor /></el-icon>
                            <span>攻击面管理</span>
                        </el-menu-item>
                        <el-menu-item index="metrics">
                            <el-icon><data-analysis /></el-icon>
                            <span>度量指标</span>
                        </el-menu-item>
                        <el-menu-item index="security-training">
                            <el-icon><reading /></el-icon>
                            <span>安全培训</span>
                        </el-menu-item>
                    </el-menu>
                </div>
                
                <!-- 主内容区域 -->
                <div class="content-area">
                    <!-- 仪表板页面 -->
                    <div v-show="currentPage === 'dashboard'">
                        <div class="page-header">
                            <div class="page-title">安全赋能平台仪表板</div>
                            <div class="page-subtitle">全面掌握应用安全态势，提升开发安全效能</div>
                        </div>
                        
                        <!-- 统计卡片 -->
                        <div class="dashboard-cards">
                            <div class="stat-card">
                                <div style="display: flex; align-items: center;">
                                    <el-icon size="32" color="#409eff"><document /></el-icon>
                                    <div style="margin-left: 16px;">
                                        <div style="color: #909399; font-size: 14px;">本月评估项目</div>
                                        <div class="stat-number" style="color: #409eff;">156</div>
                                        <div style="color: #67c23a; font-size: 12px;">↑ 12% 较上月</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div style="display: flex; align-items: center;">
                                    <el-icon size="32" color="#f56c6c"><warning /></el-icon>
                                    <div style="margin-left: 16px;">
                                        <div style="color: #909399; font-size: 14px;">发现安全漏洞</div>
                                        <div class="stat-number" style="color: #f56c6c;">2,847</div>
                                        <div style="color: #f56c6c; font-size: 12px;">↓ 8% 较上月</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div style="display: flex; align-items: center;">
                                    <el-icon size="32" color="#67c23a"><check /></el-icon>
                                    <div style="margin-left: 16px;">
                                        <div style="color: #909399; font-size: 14px;">漏洞修复率</div>
                                        <div class="stat-number" style="color: #67c23a;">94.2%</div>
                                        <div style="color: #67c23a; font-size: 12px;">↑ 3% 较上月</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div style="display: flex; align-items: center;">
                                    <el-icon size="32" color="#e6a23c"><cpu /></el-icon>
                                    <div style="margin-left: 16px;">
                                        <div style="color: #909399; font-size: 14px;">自动化覆盖率</div>
                                        <div class="stat-number" style="color: #e6a23c;">87.5%</div>
                                        <div style="color: #67c23a; font-size: 12px;">↑ 5% 较上月</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 功能模块卡片 -->
                        <div class="module-grid">
                            <div class="module-card" @click="openThreatModeling()">
                                <div class="module-header">
                                    <el-icon class="module-icon"><warning /></el-icon>
                                    <div class="module-title">轻量级威胁建模</div>
                                </div>
                                <div class="module-description">
                                    基于场景化的轻量级威胁建模，通过问卷调研、场景分析自动匹配安全需求，输出针对性的安全设计方案。
                                </div>
                                <ul class="module-features">
                                    <li>问卷调研与场景分析</li>
                                    <li>安全威胁库自动匹配</li>
                                    <li>安全需求基线生成</li>
                                    <li>安全设计方案输出</li>
                                </ul>
                            </div>
                            
                            <div class="module-card" @click="openAsocTesting()">
                                <div class="module-header">
                                    <el-icon class="module-icon"><cpu /></el-icon>
                                    <div class="module-title">ASOC安全测试编排</div>
                                </div>
                                <div class="module-description">
                                    应用安全编排与关联，统一管控各类安全扫描工具，提供流程编排和漏洞关联分析能力。
                                </div>
                                <ul class="module-features">
                                    <li>扫描工具引擎化编排</li>
                                    <li>策略剧本自动执行</li>
                                    <li>漏洞统一管理去重</li>
                                    <li>风险等级重新定义</li>
                                </ul>
                            </div>
                            
                            <div class="module-card" @click="navigateTo('security-requirements')">
                                <div class="module-header">
                                    <el-icon class="module-icon"><document /></el-icon>
                                    <div class="module-title">安全需求库</div>
                                </div>
                                <div class="module-description">
                                    整合法律法规、行业监管、公司策略和最佳实践，形成场景化的安全需求库。
                                </div>
                                <ul class="module-features">
                                    <li>多源安全需求整合</li>
                                    <li>场景化需求匹配</li>
                                    <li>需求基线自动生成</li>
                                    <li>安全编码案例库</li>
                                </ul>
                            </div>
                            
                            <div class="module-card" @click="navigateTo('data-security')">
                                <div class="module-header">
                                    <el-icon class="module-icon"><lock /></el-icon>
                                    <div class="module-title">数据安全评估</div>
                                </div>
                                <div class="module-description">
                                    内嵌数据安全评估流程，实现数据分类分级、敏感数据识别和保护措施验证。
                                </div>
                                <ul class="module-features">
                                    <li>数据分类分级管理</li>
                                    <li>敏感数据自动识别</li>
                                    <li>保护措施验证</li>
                                    <li>隐私设计PbD实现</li>
                                </ul>
                            </div>
                            
                            <div class="module-card" @click="navigateTo('attack-surface')">
                                <div class="module-header">
                                    <el-icon class="module-icon"><monitor /></el-icon>
                                    <div class="module-title">攻击面管理</div>
                                </div>
                                <div class="module-description">
                                    全面管理应用攻击面资产，包括服务、API、组件等，构建完整的资产图谱。
                                </div>
                                <ul class="module-features">
                                    <li>攻击面资产登记</li>
                                    <li>应用资产图谱构建</li>
                                    <li>供应链风险管理</li>
                                    <li>资产台账维护</li>
                                </ul>
                            </div>
                            
                            <div class="module-card" @click="openMetrics()">
                                <div class="module-header">
                                    <el-icon class="module-icon"><data-analysis /></el-icon>
                                    <div class="module-title">安全度量指标</div>
                                </div>
                                <div class="module-description">
                                    多维度安全度量指标体系，客观反映开发安全水位和改进效果。
                                </div>
                                <ul class="module-features">
                                    <li>安全评估度量指标</li>
                                    <li>工具扫描效果度量</li>
                                    <li>漏洞管理度量</li>
                                    <li>可视化报表展示</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他页面占位符 -->
                    <div v-show="currentPage !== 'dashboard'">
                        <div class="page-header">
                            <div class="page-title">{{ getPageTitle() }}</div>
                            <div class="page-subtitle">{{ getPageSubtitle() }}</div>
                        </div>
                        <el-card>
                            <div style="text-align: center; padding: 40px;">
                                <el-icon size="64" color="#c0c4cc"><document /></el-icon>
                                <div style="margin-top: 16px; color: #909399;">
                                    {{ getPageTitle() }}模块正在开发中...
                                </div>
                                <el-button type="primary" style="margin-top: 16px;" @click="navigateTo('dashboard')">
                                    返回仪表板
                                </el-button>
                            </div>
                        </el-card>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    activeMenu: 'dashboard',
                    currentPage: 'dashboard'
                };
            },
            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },
                navigateTo(page) {
                    this.activeMenu = page;
                    this.currentPage = page;
                },
                openThreatModeling() {
                    window.open('threat-modeling.html', '_blank');
                },
                openMetrics() {
                    window.open('metrics-dashboard.html', '_blank');
                },
                openAsocTesting() {
                    window.open('asoc-testing.html', '_blank');
                },
                getPageTitle() {
                    const titles = {
                        'dashboard': '仪表板',
                        'threat-modeling': '轻量级威胁建模',
                        'security-requirements': '安全需求库管理',
                        'asoc-testing': 'ASOC安全测试编排',
                        'data-security': '数据安全评估',
                        'attack-surface': '攻击面管理',
                        'metrics': '安全度量指标',
                        'security-training': '安全培训'
                    };
                    return titles[this.currentPage] || '未知页面';
                },
                getPageSubtitle() {
                    const subtitles = {
                        'threat-modeling': '基于场景化的轻量级威胁建模，自动生成安全需求基线',
                        'security-requirements': '整合多源安全需求，构建场景化需求库',
                        'asoc-testing': '应用安全编排与关联，统一管控安全测试工具',
                        'data-security': '数据安全评估内嵌，实现隐私设计PbD',
                        'attack-surface': '全面管理应用攻击面，构建资产安全图谱',
                        'metrics': '多维度安全度量，客观反映安全水位',
                        'security-training': '安全培训与认证，提升团队安全意识'
                    };
                    return subtitles[this.currentPage] || '';
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
