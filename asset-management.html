<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资产管理 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .main-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e4e7ed;
        }

        .search-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .asset-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .asset-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .asset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .asset-name {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }

        .asset-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #606266;
        }

        .info-label {
            font-weight: bold;
            margin-right: 8px;
            min-width: 80px;
        }

        .asset-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .security-indicators {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
        }

        .indicator-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #909399;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .asset-topology {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .topology-node {
            display: inline-block;
            background: #f0f9ff;
            border: 2px solid #409eff;
            border-radius: 8px;
            padding: 12px 16px;
            margin: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .topology-node:hover {
            background: #409eff;
            color: white;
        }

        .topology-connection {
            display: inline-block;
            width: 40px;
            height: 2px;
            background: #409eff;
            margin: 0 8px;
            vertical-align: middle;
        }

        .vulnerability-item {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .vuln-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .vuln-title {
            font-weight: bold;
            color: #cf1322;
        }

        .port-item {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 8px 12px;
            margin: 4px;
            display: inline-block;
            font-size: 12px;
        }

        .port-number {
            font-weight: bold;
            color: #389e0d;
        }

        .port-service {
            color: #595959;
            margin-left: 8px;
        }

        .scan-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .scan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .scan-type {
            font-weight: bold;
            color: #303133;
        }

        .scan-time {
            font-size: 12px;
            color: #909399;
        }

        .scan-summary {
            color: #606266;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 资产管理
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="overview">
                            <el-icon><odometer /></el-icon>
                            <span>资产概览</span>
                        </el-menu-item>
                        <el-menu-item index="domains">
                            <el-icon><link /></el-icon>
                            <span>域名资产</span>
                        </el-menu-item>
                        <el-menu-item index="ips">
                            <el-icon><monitor /></el-icon>
                            <span>IP资产</span>
                        </el-menu-item>
                        <el-menu-item index="services">
                            <el-icon><cpu /></el-icon>
                            <span>服务资产</span>
                        </el-menu-item>
                        <el-menu-item index="apis">
                            <el-icon><connection /></el-icon>
                            <span>API资产</span>
                        </el-menu-item>
                        <el-menu-item index="topology">
                            <el-icon><share /></el-icon>
                            <span>资产拓扑</span>
                        </el-menu-item>
                        <el-menu-item index="discovery">
                            <el-icon><search /></el-icon>
                            <span>资产发现</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 主内容区域 -->
                <div class="content-area">
                    <!-- 资产概览 -->
                    <div v-show="currentPage === 'overview'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">资产管理概览</div>
                                <div class="page-subtitle">统一管理和监控所有IT资产的安全状态</div>
                            </div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #409eff;">{{ totalAssets }}</div>
                                <div class="stat-label">资产总数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #67c23a;">{{ domainAssets }}</div>
                                <div class="stat-label">域名资产</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #e6a23c;">{{ ipAssets }}</div>
                                <div class="stat-label">IP资产</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #f56c6c;">{{ riskAssets }}</div>
                                <div class="stat-label">高风险资产</div>
                            </div>
                        </div>

                        <div class="main-content">
                            <div class="section-title">最新发现的资产</div>
                            <div v-for="asset in recentAssets" :key="asset.id" class="asset-card" @click="viewAssetDetail(asset)">
                                <div class="asset-header">
                                    <div class="asset-name">{{ asset.name }}</div>
                                    <div>
                                        <el-tag :type="getAssetTypeColor(asset.type)" size="small">
                                            {{ asset.type }}
                                        </el-tag>
                                        <el-tag :type="getRiskLevelType(asset.riskLevel)" size="small" style="margin-left: 8px;">
                                            {{ asset.riskLevel }}
                                        </el-tag>
                                    </div>
                                </div>

                                <div class="asset-info">
                                    <div class="info-item">
                                        <span class="info-label">所属应用:</span>
                                        <span>{{ asset.application }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">发现时间:</span>
                                        <span>{{ asset.discoveredAt }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">最后扫描:</span>
                                        <span>{{ asset.lastScan }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">负责人:</span>
                                        <span>{{ asset.owner }}</span>
                                    </div>
                                </div>

                                <div class="security-indicators">
                                    <div class="indicator-item">
                                        <div class="indicator-dot" :style="{ backgroundColor: asset.securityStatus.ssl ? '#67c23a' : '#f56c6c' }"></div>
                                        SSL证书: {{ asset.securityStatus.ssl ? '正常' : '异常' }}
                                    </div>
                                    <div class="indicator-item">
                                        <div class="indicator-dot" :style="{ backgroundColor: asset.securityStatus.ports ? '#67c23a' : '#f56c6c' }"></div>
                                        端口扫描: {{ asset.securityStatus.ports ? '正常' : '异常' }}
                                    </div>
                                    <div class="indicator-item">
                                        <div class="indicator-dot" :style="{ backgroundColor: asset.securityStatus.vulnerabilities ? '#f56c6c' : '#67c23a' }"></div>
                                        漏洞状态: {{ asset.securityStatus.vulnerabilities ? '存在漏洞' : '无漏洞' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 域名资产 -->
                    <div v-show="currentPage === 'domains'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">域名资产管理</div>
                                <div class="page-subtitle">管理和监控所有域名资产</div>
                            </div>
                            <el-button type="primary" @click="showAddDomainDialog = true">
                                <el-icon><plus /></el-icon>
                                添加域名
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="toolbar">
                                <div class="search-bar">
                                    <el-input
                                        v-model="searchKeyword"
                                        placeholder="搜索域名"
                                        style="width: 300px;"
                                        clearable>
                                        <template #prefix>
                                            <el-icon><search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="riskFilter" placeholder="风险筛选" style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="高风险" value="high"></el-option>
                                        <el-option label="中风险" value="medium"></el-option>
                                        <el-option label="低风险" value="low"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button @click="scanAllDomains">
                                        <el-icon><refresh /></el-icon>
                                        批量扫描
                                    </el-button>
                                    <el-button @click="exportDomains">
                                        <el-icon><download /></el-icon>
                                        导出
                                    </el-button>
                                </div>
                            </div>

                            <div v-for="domain in filteredDomains" :key="domain.id" class="asset-card" @click="viewDomainDetail(domain)">
                                <div class="asset-header">
                                    <div class="asset-name">{{ domain.name }}</div>
                                    <div>
                                        <el-tag type="info" size="small">域名</el-tag>
                                        <el-tag :type="getRiskLevelType(domain.riskLevel)" size="small" style="margin-left: 8px;">
                                            {{ domain.riskLevel }}
                                        </el-tag>
                                    </div>
                                </div>

                                <div class="asset-info">
                                    <div class="info-item">
                                        <span class="info-label">IP地址:</span>
                                        <span>{{ domain.ip }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">SSL证书:</span>
                                        <span :style="{ color: domain.sslStatus === '正常' ? '#67c23a' : '#f56c6c' }">
                                            {{ domain.sslStatus }}
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">到期时间:</span>
                                        <span>{{ domain.expiryDate }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">所属应用:</span>
                                        <span>{{ domain.application }}</span>
                                    </div>
                                </div>

                                <div class="asset-actions" @click.stop>
                                    <el-button size="small" type="primary" @click="scanDomain(domain)">扫描</el-button>
                                    <el-button size="small" @click="editDomain(domain)">编辑</el-button>
                                    <el-button size="small" @click="viewDomainVulns(domain)">漏洞</el-button>
                                    <el-dropdown>
                                        <el-button size="small">
                                            更多<el-icon><arrow-down /></el-icon>
                                        </el-button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item @click="renewSSL(domain)">续期SSL</el-dropdown-item>
                                                <el-dropdown-item @click="viewDNSRecord(domain)">DNS记录</el-dropdown-item>
                                                <el-dropdown-item divided @click="deleteDomain(domain)">删除域名</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- IP资产 -->
                    <div v-show="currentPage === 'ips'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">IP资产管理</div>
                                <div class="page-subtitle">管理和监控所有IP地址资产</div>
                            </div>
                            <el-button type="primary" @click="showAddIPDialog = true">
                                <el-icon><plus /></el-icon>
                                添加IP
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div v-for="ip in ipAssetsList" :key="ip.id" class="asset-card" @click="viewIPDetail(ip)">
                                <div class="asset-header">
                                    <div class="asset-name">{{ ip.address }}</div>
                                    <div>
                                        <el-tag type="warning" size="small">IP地址</el-tag>
                                        <el-tag :type="getRiskLevelType(ip.riskLevel)" size="small" style="margin-left: 8px;">
                                            {{ ip.riskLevel }}
                                        </el-tag>
                                    </div>
                                </div>

                                <div class="asset-info">
                                    <div class="info-item">
                                        <span class="info-label">地理位置:</span>
                                        <span>{{ ip.location }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">ISP:</span>
                                        <span>{{ ip.isp }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">开放端口:</span>
                                        <span>{{ ip.openPorts.length }}个</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">最后扫描:</span>
                                        <span>{{ ip.lastScan }}</span>
                                    </div>
                                </div>

                                <div style="margin-top: 12px;">
                                    <div style="margin-bottom: 8px; font-weight: bold; color: #303133;">开放端口:</div>
                                    <div>
                                        <span v-for="port in ip.openPorts.slice(0, 8)" :key="port.number" class="port-item">
                                            <span class="port-number">{{ port.number }}</span>
                                            <span class="port-service">{{ port.service }}</span>
                                        </span>
                                        <span v-if="ip.openPorts.length > 8" class="port-item">
                                            +{{ ip.openPorts.length - 8 }}个
                                        </span>
                                    </div>
                                </div>

                                <div class="asset-actions" @click.stop>
                                    <el-button size="small" type="primary" @click="scanIP(ip)">端口扫描</el-button>
                                    <el-button size="small" @click="viewIPVulns(ip)">漏洞</el-button>
                                    <el-button size="small" @click="viewIPServices(ip)">服务</el-button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资产拓扑 -->
                    <div v-show="currentPage === 'topology'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">资产拓扑图</div>
                                <div class="page-subtitle">可视化展示资产间的关联关系</div>
                            </div>
                        </div>

                        <div class="asset-topology">
                            <div class="section-title">网络拓扑结构</div>
                            <div style="text-align: center; padding: 40px;">
                                <div style="margin-bottom: 30px;">
                                    <div class="topology-node" @click="selectNode('internet')">
                                        🌐 互联网
                                    </div>
                                </div>

                                <div style="margin-bottom: 30px;">
                                    <div class="topology-connection"></div>
                                </div>

                                <div style="margin-bottom: 30px;">
                                    <div class="topology-node" @click="selectNode('firewall')">
                                        🔥 防火墙
                                    </div>
                                    <div class="topology-connection"></div>
                                    <div class="topology-node" @click="selectNode('loadbalancer')">
                                        ⚖️ 负载均衡
                                    </div>
                                </div>

                                <div style="margin-bottom: 30px;">
                                    <div class="topology-connection"></div>
                                </div>

                                <div style="margin-bottom: 30px;">
                                    <div class="topology-node" @click="selectNode('web1')">
                                        🖥️ Web服务器1
                                    </div>
                                    <div class="topology-node" @click="selectNode('web2')">
                                        🖥️ Web服务器2
                                    </div>
                                    <div class="topology-node" @click="selectNode('api')">
                                        🔌 API服务器
                                    </div>
                                </div>

                                <div style="margin-bottom: 30px;">
                                    <div class="topology-connection"></div>
                                </div>

                                <div>
                                    <div class="topology-node" @click="selectNode('db1')">
                                        🗄️ 数据库1
                                    </div>
                                    <div class="topology-node" @click="selectNode('db2')">
                                        🗄️ 数据库2
                                    </div>
                                    <div class="topology-node" @click="selectNode('cache')">
                                        💾 缓存服务
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="main-content" v-if="selectedNode">
                            <div class="section-title">节点详情: {{ selectedNode.name }}</div>
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="节点类型">{{ selectedNode.type }}</el-descriptions-item>
                                <el-descriptions-item label="IP地址">{{ selectedNode.ip }}</el-descriptions-item>
                                <el-descriptions-item label="状态">
                                    <el-tag :type="selectedNode.status === '正常' ? 'success' : 'danger'">
                                        {{ selectedNode.status }}
                                    </el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="风险等级">
                                    <el-tag :type="getRiskLevelType(selectedNode.riskLevel)">
                                        {{ selectedNode.riskLevel }}
                                    </el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="最后扫描">{{ selectedNode.lastScan }}</el-descriptions-item>
                                <el-descriptions-item label="开放端口">{{ selectedNode.openPorts }}</el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </div>

                    <!-- 资产发现 -->
                    <div v-show="currentPage === 'discovery'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">资产发现</div>
                                <div class="page-subtitle">自动发现和识别网络中的资产</div>
                            </div>
                            <el-button type="primary" @click="startDiscovery" :loading="discovering">
                                <el-icon><search /></el-icon>
                                开始发现
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="section-title">发现配置</div>
                            <el-form :model="discoveryConfig" label-width="120px" style="margin-bottom: 30px;">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="扫描范围">
                                            <el-input v-model="discoveryConfig.range" placeholder="例如: ***********/24"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="扫描类型">
                                            <el-checkbox-group v-model="discoveryConfig.types">
                                                <el-checkbox label="ping">Ping扫描</el-checkbox>
                                                <el-checkbox label="port">端口扫描</el-checkbox>
                                                <el-checkbox label="service">服务识别</el-checkbox>
                                                <el-checkbox label="os">操作系统识别</el-checkbox>
                                            </el-checkbox-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>

                            <div class="section-title">扫描结果</div>
                            <div v-for="result in discoveryResults" :key="result.id" class="scan-result">
                                <div class="scan-header">
                                    <div class="scan-type">{{ result.type }}</div>
                                    <div class="scan-time">{{ result.time }}</div>
                                </div>
                                <div class="scan-summary">{{ result.summary }}</div>
                                <div style="margin-top: 8px;">
                                    <el-button size="small" type="primary" @click="addToAssets(result)">
                                        添加到资产库
                                    </el-button>
                                    <el-button size="small" @click="viewDiscoveryDetail(result)">
                                        查看详情
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    activeMenu: 'overview',
                    currentPage: 'overview',
                    searchKeyword: '',
                    riskFilter: 'all',
                    showAddDomainDialog: false,
                    showAddIPDialog: false,
                    discovering: false,
                    selectedNode: null,

                    // 统计数据
                    totalAssets: 342,
                    domainAssets: 156,
                    ipAssets: 89,
                    riskAssets: 23,

                    // 资产发现配置
                    discoveryConfig: {
                        range: '***********/24',
                        types: ['ping', 'port']
                    },

                    // 最新资产
                    recentAssets: [
                        {
                            id: 1,
                            name: 'api.trading.com',
                            type: '域名',
                            application: '证券交易系统',
                            discoveredAt: '2024-01-15',
                            lastScan: '2024-01-15 14:30',
                            owner: '李四',
                            riskLevel: '中风险',
                            securityStatus: {
                                ssl: true,
                                ports: true,
                                vulnerabilities: true
                            }
                        },
                        {
                            id: 2,
                            name: '*************',
                            type: 'IP地址',
                            application: '证券交易系统',
                            discoveredAt: '2024-01-14',
                            lastScan: '2024-01-15 10:20',
                            owner: '张三',
                            riskLevel: '高风险',
                            securityStatus: {
                                ssl: false,
                                ports: false,
                                vulnerabilities: true
                            }
                        },
                        {
                            id: 3,
                            name: 'mobile-api.example.com',
                            type: 'API接口',
                            application: '移动交易APP',
                            discoveredAt: '2024-01-13',
                            lastScan: '2024-01-14 16:45',
                            owner: '王五',
                            riskLevel: '低风险',
                            securityStatus: {
                                ssl: true,
                                ports: true,
                                vulnerabilities: false
                            }
                        }
                    ],

                    // 域名资产
                    domains: [
                        {
                            id: 1,
                            name: 'trading.example.com',
                            ip: '*************',
                            sslStatus: '正常',
                            expiryDate: '2024-12-31',
                            application: '证券交易系统',
                            riskLevel: '低风险'
                        },
                        {
                            id: 2,
                            name: 'api.trading.com',
                            ip: '*************',
                            sslStatus: '即将过期',
                            expiryDate: '2024-02-15',
                            application: '证券交易系统',
                            riskLevel: '中风险'
                        },
                        {
                            id: 3,
                            name: 'mobile.example.com',
                            ip: '*************',
                            sslStatus: '正常',
                            expiryDate: '2024-08-20',
                            application: '移动交易APP',
                            riskLevel: '低风险'
                        }
                    ],

                    // IP资产
                    ipAssetsList: [
                        {
                            id: 1,
                            address: '*************',
                            location: '北京',
                            isp: '阿里云',
                            riskLevel: '中风险',
                            lastScan: '2024-01-15 14:30',
                            openPorts: [
                                { number: 80, service: 'HTTP' },
                                { number: 443, service: 'HTTPS' },
                                { number: 22, service: 'SSH' },
                                { number: 3306, service: 'MySQL' },
                                { number: 6379, service: 'Redis' },
                                { number: 8080, service: 'HTTP-Alt' },
                                { number: 9200, service: 'Elasticsearch' },
                                { number: 5432, service: 'PostgreSQL' },
                                { number: 27017, service: 'MongoDB' }
                            ]
                        },
                        {
                            id: 2,
                            address: '*************',
                            location: '上海',
                            isp: '腾讯云',
                            riskLevel: '高风险',
                            lastScan: '2024-01-15 10:20',
                            openPorts: [
                                { number: 80, service: 'HTTP' },
                                { number: 443, service: 'HTTPS' },
                                { number: 21, service: 'FTP' },
                                { number: 23, service: 'Telnet' },
                                { number: 3389, service: 'RDP' }
                            ]
                        }
                    ],

                    // 拓扑节点数据
                    topologyNodes: {
                        internet: { name: '互联网', type: '网络', ip: '-', status: '正常', riskLevel: '低风险', lastScan: '-', openPorts: '-' },
                        firewall: { name: '防火墙', type: '安全设备', ip: '***********', status: '正常', riskLevel: '低风险', lastScan: '2024-01-15 14:00', openPorts: '22,443' },
                        loadbalancer: { name: '负载均衡', type: '网络设备', ip: '***********0', status: '正常', riskLevel: '低风险', lastScan: '2024-01-15 14:00', openPorts: '80,443' },
                        web1: { name: 'Web服务器1', type: '应用服务器', ip: '*************', status: '正常', riskLevel: '中风险', lastScan: '2024-01-15 14:30', openPorts: '80,443,22' },
                        web2: { name: 'Web服务器2', type: '应用服务器', ip: '*************', status: '正常', riskLevel: '中风险', lastScan: '2024-01-15 14:30', openPorts: '80,443,22' },
                        api: { name: 'API服务器', type: '应用服务器', ip: '*************', status: '正常', riskLevel: '低风险', lastScan: '2024-01-15 14:30', openPorts: '8080,8443' },
                        db1: { name: '数据库1', type: '数据库服务器', ip: '*************', status: '正常', riskLevel: '高风险', lastScan: '2024-01-15 14:30', openPorts: '3306' },
                        db2: { name: '数据库2', type: '数据库服务器', ip: '*************', status: '正常', riskLevel: '高风险', lastScan: '2024-01-15 14:30', openPorts: '5432' },
                        cache: { name: '缓存服务', type: '缓存服务器', ip: '*************', status: '正常', riskLevel: '中风险', lastScan: '2024-01-15 14:30', openPorts: '6379' }
                    },

                    // 发现结果
                    discoveryResults: [
                        {
                            id: 1,
                            type: '主机发现',
                            time: '2024-01-15 14:30',
                            summary: '发现3台新主机: *************, *************, *************'
                        },
                        {
                            id: 2,
                            type: '服务发现',
                            time: '2024-01-15 14:25',
                            summary: '发现新的Web服务运行在端口8080，可能存在未授权访问风险'
                        },
                        {
                            id: 3,
                            type: '端口扫描',
                            time: '2024-01-15 14:20',
                            summary: '*************开放了新端口9200(Elasticsearch)，建议进行安全配置检查'
                        }
                    ]
                };
            },

            computed: {
                filteredDomains() {
                    let filtered = this.domains;

                    if (this.searchKeyword) {
                        filtered = filtered.filter(domain =>
                            domain.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
                        );
                    }

                    if (this.riskFilter !== 'all') {
                        filtered = filtered.filter(domain => {
                            if (this.riskFilter === 'high') return domain.riskLevel === '高风险';
                            if (this.riskFilter === 'medium') return domain.riskLevel === '中风险';
                            if (this.riskFilter === 'low') return domain.riskLevel === '低风险';
                            return true;
                        });
                    }

                    return filtered;
                }
            },

            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },

                getAssetTypeColor(type) {
                    switch (type) {
                        case '域名': return 'primary';
                        case 'IP地址': return 'warning';
                        case 'API接口': return 'success';
                        case '服务': return 'info';
                        default: return 'info';
                    }
                },

                getRiskLevelType(riskLevel) {
                    switch (riskLevel) {
                        case '高风险': return 'danger';
                        case '中风险': return 'warning';
                        case '低风险': return 'success';
                        default: return 'info';
                    }
                },

                viewAssetDetail(asset) {
                    ElMessage.info(`查看资产"${asset.name}"详情功能开发中...`);
                },

                viewDomainDetail(domain) {
                    ElMessage.info(`查看域名"${domain.name}"详情功能开发中...`);
                },

                scanDomain(domain) {
                    ElMessage.success(`正在扫描域名"${domain.name}"...`);
                },

                editDomain(domain) {
                    ElMessage.info(`编辑域名"${domain.name}"功能开发中...`);
                },

                viewDomainVulns(domain) {
                    ElMessage.info(`查看域名"${domain.name}"的漏洞功能开发中...`);
                },

                renewSSL(domain) {
                    ElMessageBox.confirm(
                        `是否为域名"${domain.name}"续期SSL证书？`,
                        '续期SSL证书',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        ElMessage.success('SSL证书续期申请已提交');
                    });
                },

                viewDNSRecord(domain) {
                    ElMessage.info(`查看域名"${domain.name}"的DNS记录功能开发中...`);
                },

                deleteDomain(domain) {
                    ElMessageBox.confirm(
                        `确定要删除域名"${domain.name}"吗？`,
                        '删除域名',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        const index = this.domains.findIndex(d => d.id === domain.id);
                        if (index > -1) {
                            this.domains.splice(index, 1);
                            ElMessage.success('域名删除成功');
                        }
                    });
                },

                viewIPDetail(ip) {
                    ElMessage.info(`查看IP"${ip.address}"详情功能开发中...`);
                },

                scanIP(ip) {
                    ElMessage.success(`正在扫描IP"${ip.address}"...`);
                },

                viewIPVulns(ip) {
                    ElMessage.info(`查看IP"${ip.address}"的漏洞功能开发中...`);
                },

                viewIPServices(ip) {
                    ElMessage.info(`查看IP"${ip.address}"的服务功能开发中...`);
                },

                selectNode(nodeKey) {
                    this.selectedNode = this.topologyNodes[nodeKey];
                    ElMessage.info(`已选择节点: ${this.selectedNode.name}`);
                },

                startDiscovery() {
                    if (!this.discoveryConfig.range) {
                        ElMessage.warning('请输入扫描范围');
                        return;
                    }

                    this.discovering = true;
                    ElMessage.success('资产发现已启动');

                    // 模拟发现过程
                    setTimeout(() => {
                        this.discoveryResults.unshift({
                            id: this.discoveryResults.length + 1,
                            type: '新发现',
                            time: new Date().toLocaleString('zh-CN'),
                            summary: `在${this.discoveryConfig.range}范围内发现2台新主机和3个新服务`
                        });
                        this.discovering = false;
                        ElMessage.success('资产发现完成');
                    }, 3000);
                },

                addToAssets(result) {
                    ElMessage.success(`发现结果已添加到资产库`);
                },

                viewDiscoveryDetail(result) {
                    ElMessage.info(`查看发现详情功能开发中...`);
                },

                scanAllDomains() {
                    ElMessage.success('批量域名扫描已启动');
                },

                exportDomains() {
                    ElMessage.success('域名资产导出成功');
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
