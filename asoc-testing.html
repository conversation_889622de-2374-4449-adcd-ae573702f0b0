<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASOC安全测试编排 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .main-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .side-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .tool-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .tool-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .tool-card.active {
            border-color: #409eff;
            background-color: #ecf5ff;
        }

        .tool-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }

        .tool-name {
            font-weight: bold;
            color: #303133;
        }

        .tool-status {
            font-size: 12px;
        }

        .tool-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .tool-stats {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #909399;
        }

        .playbook-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .playbook-item:hover {
            border-color: #409eff;
            background-color: #ecf5ff;
        }

        .playbook-item.active {
            border-color: #409eff;
            background-color: #ecf5ff;
        }

        .playbook-name {
            font-weight: bold;
            color: #303133;
            margin-bottom: 4px;
        }

        .playbook-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .playbook-tools {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .vulnerability-item {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .vuln-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .vuln-title {
            font-weight: bold;
            color: #303133;
        }

        .vuln-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .vuln-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #909399;
        }

        .stat-card {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 16px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .cluster-node {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .node-name {
            font-weight: bold;
            color: #303133;
        }

        .node-info {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #909399;
        }

        .execution-log {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-line {
            margin-bottom: 4px;
        }

        .log-timestamp {
            color: #569cd6;
        }

        .log-level-info {
            color: #4ec9b0;
        }

        .log-level-warn {
            color: #dcdcaa;
        }

        .log-level-error {
            color: #f44747;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - ASOC安全测试编排
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>测试概览</span>
                        </el-menu-item>
                        <el-menu-item index="tools">
                            <el-icon><cpu /></el-icon>
                            <span>扫描工具</span>
                        </el-menu-item>
                        <el-menu-item index="playbooks">
                            <el-icon><document /></el-icon>
                            <span>策略剧本</span>
                        </el-menu-item>
                        <el-menu-item index="vulnerabilities">
                            <el-icon><warning /></el-icon>
                            <span>漏洞管理</span>
                        </el-menu-item>
                        <el-menu-item index="cluster">
                            <el-icon><monitor /></el-icon>
                            <span>集群管理</span>
                        </el-menu-item>
                        <el-menu-item index="reports">
                            <el-icon><data-analysis /></el-icon>
                            <span>测试报告</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 主内容区域 -->
                <div class="content-area">
                    <div class="page-header">
                        <div class="page-title">ASOC安全测试编排与关联</div>
                        <div class="page-subtitle">统一管控安全扫描工具，提供流程编排和漏洞关联分析能力</div>
                    </div>

                    <!-- 测试概览页面 -->
                    <div v-show="currentPage === 'dashboard'">
                        <div class="dashboard-grid">
                            <div class="main-panel">
                                <div class="section-title">实时执行状态</div>
                                <div class="execution-log">
                                    <div v-for="log in executionLogs" :key="log.id" class="log-line">
                                        <span class="log-timestamp">[{{ log.timestamp }}]</span>
                                        <span :class="'log-level-' + log.level">{{ log.level.toUpperCase() }}</span>
                                        {{ log.message }}
                                    </div>
                                </div>

                                <div style="margin-top: 20px;">
                                    <el-button type="primary" @click="startScan" :loading="scanning">
                                        <el-icon><cpu /></el-icon>
                                        启动安全扫描
                                    </el-button>
                                    <el-button @click="stopScan" :disabled="!scanning">
                                        <el-icon><video-pause /></el-icon>
                                        停止扫描
                                    </el-button>
                                    <el-button @click="clearLogs">
                                        <el-icon><delete /></el-icon>
                                        清空日志
                                    </el-button>
                                </div>
                            </div>

                            <div class="side-panel">
                                <div class="section-title">统计概览</div>
                                <div class="stat-card">
                                    <div class="stat-number" style="color: #409eff;">{{ totalScans }}</div>
                                    <div class="stat-label">总扫描次数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" style="color: #f56c6c;">{{ totalVulnerabilities }}</div>
                                    <div class="stat-label">发现漏洞</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" style="color: #67c23a;">{{ fixedVulnerabilities }}</div>
                                    <div class="stat-label">已修复</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" style="color: #e6a23c;">{{ activeTools }}</div>
                                    <div class="stat-label">活跃工具</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 扫描工具页面 -->
                    <div v-show="currentPage === 'tools'">
                        <div class="section-title">扫描工具引擎管理</div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                            <div v-for="tool in scanTools" :key="tool.id"
                                 class="tool-card"
                                 :class="{ active: tool.enabled }"
                                 @click="toggleTool(tool.id)">
                                <div class="tool-header">
                                    <div class="tool-name">{{ tool.name }}</div>
                                    <el-tag :type="tool.enabled ? 'success' : 'info'" size="small">
                                        {{ tool.enabled ? '已启用' : '已禁用' }}
                                    </el-tag>
                                </div>
                                <div class="tool-description">{{ tool.description }}</div>
                                <div class="tool-stats">
                                    <span>版本: {{ tool.version }}</span>
                                    <span>节点: {{ tool.nodes }}</span>
                                    <span>扫描: {{ tool.scans }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 策略剧本页面 -->
                    <div v-show="currentPage === 'playbooks'">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div class="section-title">扫描策略剧本</div>
                            <el-button type="primary" @click="createPlaybook">
                                <el-icon><plus /></el-icon>
                                创建剧本
                            </el-button>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 16px;">
                            <div v-for="playbook in playbooks" :key="playbook.id"
                                 class="playbook-item"
                                 :class="{ active: selectedPlaybook === playbook.id }"
                                 @click="selectPlaybook(playbook.id)">
                                <div class="playbook-name">{{ playbook.name }}</div>
                                <div class="playbook-description">{{ playbook.description }}</div>
                                <div class="playbook-tools">
                                    <el-tag v-for="tool in playbook.tools" :key="tool" size="small" type="info">
                                        {{ tool }}
                                    </el-tag>
                                </div>
                                <div style="margin-top: 12px; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 12px; color: #909399;">
                                        优先级: {{ playbook.priority }} | 执行次数: {{ playbook.executions }}
                                    </div>
                                    <div>
                                        <el-button size="small" type="primary" @click.stop="executePlaybook(playbook.id)">
                                            执行
                                        </el-button>
                                        <el-button size="small" @click.stop="editPlaybook(playbook.id)">
                                            编辑
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 漏洞管理页面 -->
                    <div v-show="currentPage === 'vulnerabilities'">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div class="section-title">漏洞统一管理</div>
                            <div>
                                <el-select v-model="vulnFilter" placeholder="筛选漏洞" style="margin-right: 12px;">
                                    <el-option label="全部" value="all"></el-option>
                                    <el-option label="高危" value="high"></el-option>
                                    <el-option label="中危" value="medium"></el-option>
                                    <el-option label="低危" value="low"></el-option>
                                </el-select>
                                <el-button type="primary" @click="deduplicateVulns">
                                    <el-icon><refresh /></el-icon>
                                    去重处理
                                </el-button>
                            </div>
                        </div>

                        <div v-for="vuln in filteredVulnerabilities" :key="vuln.id" class="vulnerability-item">
                            <div class="vuln-header">
                                <div class="vuln-title">{{ vuln.title }}</div>
                                <div>
                                    <el-tag :type="getSeverityType(vuln.severity)" size="small">
                                        {{ vuln.severity }}
                                    </el-tag>
                                    <el-tag type="info" size="small" style="margin-left: 8px;">
                                        {{ vuln.source }}
                                    </el-tag>
                                </div>
                            </div>
                            <div class="vuln-description">{{ vuln.description }}</div>
                            <div class="vuln-meta">
                                <span>发现时间: {{ vuln.discoveredAt }}</span>
                                <span>文件: {{ vuln.file }}</span>
                                <span>行号: {{ vuln.line }}</span>
                                <span>状态: {{ vuln.status }}</span>
                            </div>
                            <div style="margin-top: 12px;">
                                <el-button size="small" type="success" @click="fixVulnerability(vuln.id)">
                                    标记修复
                                </el-button>
                                <el-button size="small" @click="viewDetails(vuln.id)">
                                    查看详情
                                </el-button>
                                <el-button size="small" type="warning" @click="adjustSeverity(vuln.id)">
                                    调整等级
                                </el-button>
                            </div>
                        </div>
                    </div>

                    <!-- 集群管理页面 -->
                    <div v-show="currentPage === 'cluster'">
                        <div class="section-title">安全测试集群管理</div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                            <div v-for="node in clusterNodes" :key="node.id" class="cluster-node">
                                <div class="node-header">
                                    <div class="node-name">{{ node.name }}</div>
                                    <el-tag :type="getNodeStatusType(node.status)" size="small">
                                        {{ node.status }}
                                    </el-tag>
                                </div>
                                <div class="node-info">
                                    <span>CPU: {{ node.cpu }}%</span>
                                    <span>内存: {{ node.memory }}%</span>
                                    <span>任务: {{ node.tasks }}</span>
                                </div>
                                <div style="margin-top: 12px;">
                                    <el-progress :percentage="node.cpu" :color="getProgressColor(node.cpu)" size="small"></el-progress>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 测试报告页面 -->
                    <div v-show="currentPage === 'reports'">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div class="section-title">安全测试报告</div>
                            <el-button type="primary" @click="generateReport">
                                <el-icon><document /></el-icon>
                                生成报告
                            </el-button>
                        </div>

                        <el-table :data="reports" style="width: 100%">
                            <el-table-column prop="name" label="报告名称" width="200"></el-table-column>
                            <el-table-column prop="type" label="报告类型" width="120">
                                <template #default="scope">
                                    <el-tag :type="scope.row.type === '内部' ? 'success' : 'warning'" size="small">
                                        {{ scope.row.type }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="vulnerabilities" label="漏洞数量" width="100"></el-table-column>
                            <el-table-column prop="createdAt" label="生成时间" width="180"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template #default="scope">
                                    <el-tag :type="scope.row.status === '已完成' ? 'success' : 'info'" size="small">
                                        {{ scope.row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作">
                                <template #default="scope">
                                    <el-button size="small" @click="downloadReport(scope.row.id)">下载</el-button>
                                    <el-button size="small" type="primary" @click="viewReport(scope.row.id)">查看</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    activeMenu: 'dashboard',
                    currentPage: 'dashboard',
                    scanning: false,
                    selectedPlaybook: null,
                    vulnFilter: 'all',

                    // 统计数据
                    totalScans: 1247,
                    totalVulnerabilities: 2847,
                    fixedVulnerabilities: 2683,
                    activeTools: 8,

                    // 执行日志
                    executionLogs: [
                        { id: 1, timestamp: '2024-01-15 14:30:25', level: 'info', message: 'SAST扫描引擎启动成功' },
                        { id: 2, timestamp: '2024-01-15 14:30:28', level: 'info', message: '开始扫描项目: 证券交易系统' },
                        { id: 3, timestamp: '2024-01-15 14:32:15', level: 'warn', message: '发现SQL注入漏洞 - UserController.java:45' },
                        { id: 4, timestamp: '2024-01-15 14:33:42', level: 'error', message: 'XSS漏洞检测 - LoginForm.vue:128' },
                        { id: 5, timestamp: '2024-01-15 14:35:10', level: 'info', message: 'SAST扫描完成，发现12个漏洞' },
                        { id: 6, timestamp: '2024-01-15 14:35:15', level: 'info', message: 'SCA扫描引擎启动...' },
                        { id: 7, timestamp: '2024-01-15 14:36:22', level: 'warn', message: '发现高危组件漏洞: log4j-2.14.1' },
                        { id: 8, timestamp: '2024-01-15 14:37:45', level: 'info', message: 'SCA扫描完成，发现5个组件漏洞' }
                    ],

                    // 扫描工具
                    scanTools: [
                        {
                            id: 1,
                            name: 'SAST静态扫描',
                            description: '源代码静态安全分析工具，检测代码中的安全漏洞',
                            version: 'v2.1.3',
                            nodes: 4,
                            scans: 156,
                            enabled: true
                        },
                        {
                            id: 2,
                            name: 'SCA组件扫描',
                            description: '开源组件安全分析，识别第三方组件漏洞',
                            version: 'v1.8.2',
                            nodes: 2,
                            scans: 89,
                            enabled: true
                        },
                        {
                            id: 3,
                            name: 'DAST动态扫描',
                            description: '黑盒动态安全测试，模拟攻击检测运行时漏洞',
                            version: 'v3.0.1',
                            nodes: 3,
                            scans: 67,
                            enabled: true
                        },
                        {
                            id: 4,
                            name: 'IAST交互扫描',
                            description: '交互式应用安全测试，结合静态和动态分析',
                            version: 'v1.5.4',
                            nodes: 2,
                            scans: 34,
                            enabled: false
                        },
                        {
                            id: 5,
                            name: 'MAST移动扫描',
                            description: '移动应用安全测试，专门针对iOS和Android应用',
                            version: 'v2.3.1',
                            nodes: 1,
                            scans: 23,
                            enabled: true
                        },
                        {
                            id: 6,
                            name: '容器镜像扫描',
                            description: '容器镜像安全扫描，检测镜像中的漏洞和配置问题',
                            version: 'v1.9.0',
                            nodes: 2,
                            scans: 78,
                            enabled: true
                        }
                    ],

                    // 策略剧本
                    playbooks: [
                        {
                            id: 1,
                            name: 'Web应用全面扫描',
                            description: '针对Web应用的全面安全扫描策略，包括静态、动态和组件扫描',
                            tools: ['SAST', 'DAST', 'SCA'],
                            priority: '高',
                            executions: 45
                        },
                        {
                            id: 2,
                            name: '移动应用安全检测',
                            description: '移动应用专项安全检测，包括代码和运行时安全分析',
                            tools: ['MAST', 'SAST'],
                            priority: '中',
                            executions: 23
                        },
                        {
                            id: 3,
                            name: '容器化应用扫描',
                            description: '容器化应用的安全扫描策略，包括镜像和运行时检测',
                            tools: ['容器扫描', 'SAST', 'SCA'],
                            priority: '高',
                            executions: 67
                        },
                        {
                            id: 4,
                            name: '快速安全检查',
                            description: '快速的基础安全检查，适用于频繁的CI/CD流程',
                            tools: ['SAST', 'SCA'],
                            priority: '中',
                            executions: 156
                        }
                    ],

                    // 漏洞数据
                    vulnerabilities: [
                        {
                            id: 1,
                            title: 'SQL注入漏洞',
                            description: '在用户登录接口中发现SQL注入漏洞，可能导致数据库信息泄露',
                            severity: '高危',
                            source: 'SAST',
                            discoveredAt: '2024-01-15 14:32:15',
                            file: 'UserController.java',
                            line: 45,
                            status: '待修复'
                        },
                        {
                            id: 2,
                            title: 'XSS跨站脚本攻击',
                            description: '登录表单存在XSS漏洞，攻击者可能执行恶意脚本',
                            severity: '中危',
                            source: 'DAST',
                            discoveredAt: '2024-01-15 14:33:42',
                            file: 'LoginForm.vue',
                            line: 128,
                            status: '待修复'
                        },
                        {
                            id: 3,
                            title: 'Log4j组件漏洞',
                            description: '使用的Log4j版本存在远程代码执行漏洞CVE-2021-44228',
                            severity: '高危',
                            source: 'SCA',
                            discoveredAt: '2024-01-15 14:36:22',
                            file: 'pom.xml',
                            line: 67,
                            status: '已修复'
                        },
                        {
                            id: 4,
                            title: '弱密码策略',
                            description: '系统密码策略过于宽松，存在暴力破解风险',
                            severity: '低危',
                            source: 'DAST',
                            discoveredAt: '2024-01-15 14:28:10',
                            file: 'PasswordPolicy.java',
                            line: 23,
                            status: '待修复'
                        }
                    ]
                };
            },

            computed: {
                filteredVulnerabilities() {
                    if (this.vulnFilter === 'all') {
                        return this.vulnerabilities;
                    }
                    return this.vulnerabilities.filter(vuln => {
                        if (this.vulnFilter === 'high') return vuln.severity === '高危';
                        if (this.vulnFilter === 'medium') return vuln.severity === '中危';
                        if (this.vulnFilter === 'low') return vuln.severity === '低危';
                        return true;
                    });
                },

                clusterNodes() {
                    return [
                        { id: 1, name: 'scan-node-01', status: '运行中', cpu: 45, memory: 67, tasks: 3 },
                        { id: 2, name: 'scan-node-02', status: '运行中', cpu: 78, memory: 82, tasks: 5 },
                        { id: 3, name: 'scan-node-03', status: '空闲', cpu: 12, memory: 34, tasks: 0 },
                        { id: 4, name: 'scan-node-04', status: '维护中', cpu: 0, memory: 0, tasks: 0 },
                        { id: 5, name: 'scan-node-05', status: '运行中', cpu: 89, memory: 91, tasks: 7 },
                        { id: 6, name: 'scan-node-06', status: '运行中', cpu: 56, memory: 73, tasks: 2 }
                    ];
                },

                reports() {
                    return [
                        { id: 1, name: '证券交易系统安全测试报告', type: '内部', vulnerabilities: 12, createdAt: '2024-01-15 15:30:00', status: '已完成' },
                        { id: 2, name: '移动APP安全评估报告', type: '外部', vulnerabilities: 8, createdAt: '2024-01-14 10:15:00', status: '已完成' },
                        { id: 3, name: '容器镜像安全扫描报告', type: '内部', vulnerabilities: 5, createdAt: '2024-01-13 16:45:00', status: '已完成' },
                        { id: 4, name: '开源组件漏洞分析报告', type: '内部', vulnerabilities: 23, createdAt: '2024-01-12 09:20:00', status: '生成中' }
                    ];
                }
            },

            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },

                startScan() {
                    this.scanning = true;
                    ElMessage.success('安全扫描已启动');

                    // 模拟扫描过程
                    const scanMessages = [
                        { level: 'info', message: '初始化扫描环境...' },
                        { level: 'info', message: '加载扫描规则库...' },
                        { level: 'info', message: '开始SAST静态扫描...' },
                        { level: 'warn', message: '发现潜在SQL注入风险' },
                        { level: 'info', message: '开始SCA组件扫描...' },
                        { level: 'error', message: '发现高危组件漏洞' },
                        { level: 'info', message: '扫描完成，生成报告中...' }
                    ];

                    let index = 0;
                    const interval = setInterval(() => {
                        if (index < scanMessages.length) {
                            const now = new Date();
                            const timestamp = now.toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit'
                            });

                            this.executionLogs.push({
                                id: this.executionLogs.length + 1,
                                timestamp: timestamp,
                                level: scanMessages[index].level,
                                message: scanMessages[index].message
                            });
                            index++;
                        } else {
                            clearInterval(interval);
                            this.scanning = false;
                            ElMessage.success('安全扫描完成');
                        }
                    }, 2000);
                },

                stopScan() {
                    this.scanning = false;
                    ElMessage.warning('安全扫描已停止');
                },

                clearLogs() {
                    this.executionLogs = [];
                    ElMessage.info('日志已清空');
                },

                toggleTool(toolId) {
                    const tool = this.scanTools.find(t => t.id === toolId);
                    if (tool) {
                        tool.enabled = !tool.enabled;
                        ElMessage.success(`${tool.name} 已${tool.enabled ? '启用' : '禁用'}`);
                    }
                },

                createPlaybook() {
                    ElMessage.info('创建剧本功能开发中...');
                },

                selectPlaybook(playbookId) {
                    this.selectedPlaybook = playbookId;
                },

                executePlaybook(playbookId) {
                    const playbook = this.playbooks.find(p => p.id === playbookId);
                    if (playbook) {
                        ElMessage.success(`正在执行剧本: ${playbook.name}`);
                        playbook.executions++;
                    }
                },

                editPlaybook(playbookId) {
                    ElMessage.info('编辑剧本功能开发中...');
                },

                deduplicateVulns() {
                    ElMessage.success('漏洞去重处理完成，合并了3个重复漏洞');
                },

                getSeverityType(severity) {
                    switch (severity) {
                        case '高危': return 'danger';
                        case '中危': return 'warning';
                        case '低危': return 'info';
                        default: return 'info';
                    }
                },

                fixVulnerability(vulnId) {
                    const vuln = this.vulnerabilities.find(v => v.id === vulnId);
                    if (vuln) {
                        vuln.status = '已修复';
                        ElMessage.success(`漏洞 "${vuln.title}" 已标记为修复`);
                    }
                },

                viewDetails(vulnId) {
                    ElMessage.info('查看漏洞详情功能开发中...');
                },

                adjustSeverity(vulnId) {
                    ElMessage.info('调整漏洞等级功能开发中...');
                },

                getNodeStatusType(status) {
                    switch (status) {
                        case '运行中': return 'success';
                        case '空闲': return 'info';
                        case '维护中': return 'warning';
                        default: return 'info';
                    }
                },

                getProgressColor(percentage) {
                    if (percentage < 50) return '#67c23a';
                    if (percentage < 80) return '#e6a23c';
                    return '#f56c6c';
                },

                generateReport() {
                    ElMessage.success('正在生成安全测试报告...');
                },

                downloadReport(reportId) {
                    ElMessage.success('报告下载已开始');
                },

                viewReport(reportId) {
                    ElMessage.info('查看报告功能开发中...');
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
