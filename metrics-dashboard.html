<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全度量指标仪表板 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        
        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .main-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .metric-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }
        
        .metric-value {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-label {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .metric-trend {
            font-size: 12px;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .full-width-chart {
            grid-column: 1 / -1;
        }
        
        .kpi-table {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 安全度量指标
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>
            
            <div class="main-container">
                <div class="page-header">
                    <div class="page-title">安全度量指标仪表板</div>
                    <div class="page-subtitle">多维度安全度量，客观反映开发安全水位和改进效果</div>
                </div>
                
                <!-- 核心指标卡片 -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #409eff;">📊</div>
                        <div class="metric-value" style="color: #409eff;">{{ securityCoverage }}%</div>
                        <div class="metric-label">安全评估覆盖率</div>
                        <div class="metric-trend" style="color: #67c23a;">↑ 5.2% 较上月</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #67c23a;">⚡</div>
                        <div class="metric-value" style="color: #67c23a;">{{ automationRate }}%</div>
                        <div class="metric-label">安全工具自动化率</div>
                        <div class="metric-trend" style="color: #67c23a;">↑ 8.1% 较上月</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #f56c6c;">🔍</div>
                        <div class="metric-value" style="color: #f56c6c;">{{ falsePositiveRate }}%</div>
                        <div class="metric-label">漏洞误报率</div>
                        <div class="metric-trend" style="color: #67c23a;">↓ 3.4% 较上月</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #e6a23c;">🛠️</div>
                        <div class="metric-value" style="color: #e6a23c;">{{ avgFixTime }}</div>
                        <div class="metric-label">平均修复时间(天)</div>
                        <div class="metric-trend" style="color: #67c23a;">↓ 1.2天 较上月</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #909399;">🎯</div>
                        <div class="metric-value" style="color: #909399;">{{ discoveryRate }}%</div>
                        <div class="metric-label">漏洞发现率</div>
                        <div class="metric-trend" style="color: #67c23a;">↑ 12.5% 较上月</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon" style="color: #67c23a;">✅</div>
                        <div class="metric-value" style="color: #67c23a;">{{ complianceRate }}%</div>
                        <div class="metric-label">合规达标率</div>
                        <div class="metric-trend" style="color: #67c23a;">↑ 2.8% 较上月</div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="chart-grid">
                    <div class="chart-container">
                        <div class="chart-title">漏洞趋势分析</div>
                        <canvas id="vulnerabilityTrendChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-title">安全工具使用分布</div>
                        <canvas id="toolUsageChart" width="400" height="200"></canvas>
                    </div>
                </div>
                
                <div class="chart-container full-width-chart">
                    <div class="chart-title">安全评估时效性统计</div>
                    <canvas id="timelinessChart" width="800" height="300"></canvas>
                </div>
                
                <!-- KPI详细表格 -->
                <div class="kpi-table">
                    <div class="section-title">详细KPI指标</div>
                    <el-table :data="kpiData" style="width: 100%">
                        <el-table-column prop="category" label="指标类别" width="150"></el-table-column>
                        <el-table-column prop="metric" label="指标名称" width="200"></el-table-column>
                        <el-table-column prop="current" label="当前值" width="100"></el-table-column>
                        <el-table-column prop="target" label="目标值" width="100"></el-table-column>
                        <el-table-column prop="trend" label="趋势" width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.trend.includes('↑') ? 'success' : 'danger'" size="small">
                                    {{ scope.row.trend }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" label="达标状态" width="100">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '达标' ? 'success' : 'warning'" size="small">
                                    {{ scope.row.status }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="description" label="说明"></el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;
        
        createApp({
            data() {
                return {
                    securityCoverage: 94.2,
                    automationRate: 87.5,
                    falsePositiveRate: 12.3,
                    avgFixTime: 4.8,
                    discoveryRate: 89.6,
                    complianceRate: 96.1,
                    
                    kpiData: [
                        {
                            category: '安全评估',
                            metric: '安全评估覆盖率',
                            current: '94.2%',
                            target: '95%',
                            trend: '↑ 5.2%',
                            status: '接近',
                            description: '已评估项目占总项目的比例'
                        },
                        {
                            category: '安全评估',
                            metric: '安全评估时效性',
                            current: '2.3天',
                            target: '2天',
                            trend: '↓ 0.5天',
                            status: '接近',
                            description: '从项目启动到完成安全评估的平均时间'
                        },
                        {
                            category: '工具扫描',
                            metric: '扫描工具自动化率',
                            current: '87.5%',
                            target: '90%',
                            trend: '↑ 8.1%',
                            status: '接近',
                            description: '自动化扫描占总扫描次数的比例'
                        },
                        {
                            category: '工具扫描',
                            metric: '漏洞误报率',
                            current: '12.3%',
                            target: '10%',
                            trend: '↓ 3.4%',
                            status: '接近',
                            description: '误报漏洞占总发现漏洞的比例'
                        },
                        {
                            category: '漏洞管理',
                            metric: '漏洞修复率',
                            current: '94.2%',
                            target: '95%',
                            trend: '↑ 2.1%',
                            status: '接近',
                            description: '已修复漏洞占总发现漏洞的比例'
                        },
                        {
                            category: '漏洞管理',
                            metric: '平均修复时间',
                            current: '4.8天',
                            target: '5天',
                            trend: '↓ 1.2天',
                            status: '达标',
                            description: '从漏洞发现到修复完成的平均时间'
                        }
                    ]
                };
            },
            
            methods: {
                goHome() {
                    window.location.href = 'index.html';
                },
                
                initCharts() {
                    // 漏洞趋势图
                    const vulnCtx = document.getElementById('vulnerabilityTrendChart').getContext('2d');
                    new Chart(vulnCtx, {
                        type: 'line',
                        data: {
                            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                                label: '发现漏洞',
                                data: [45, 52, 38, 41, 35, 28],
                                borderColor: '#f56c6c',
                                backgroundColor: 'rgba(245, 108, 108, 0.1)',
                                tension: 0.4
                            }, {
                                label: '修复漏洞',
                                data: [42, 48, 36, 39, 33, 26],
                                borderColor: '#67c23a',
                                backgroundColor: 'rgba(103, 194, 58, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                    
                    // 工具使用分布图
                    const toolCtx = document.getElementById('toolUsageChart').getContext('2d');
                    new Chart(toolCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['SAST', 'DAST', 'SCA', 'IAST', 'MAST'],
                            datasets: [{
                                data: [35, 25, 20, 12, 8],
                                backgroundColor: [
                                    '#409eff',
                                    '#67c23a',
                                    '#e6a23c',
                                    '#f56c6c',
                                    '#909399'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                }
                            }
                        }
                    });
                    
                    // 时效性统计图
                    const timeCtx = document.getElementById('timelinessChart').getContext('2d');
                    new Chart(timeCtx, {
                        type: 'bar',
                        data: {
                            labels: ['1天内', '2天内', '3天内', '4天内', '5天内', '超过5天'],
                            datasets: [{
                                label: '项目数量',
                                data: [45, 67, 23, 12, 8, 3],
                                backgroundColor: [
                                    '#67c23a',
                                    '#67c23a',
                                    '#e6a23c',
                                    '#e6a23c',
                                    '#f56c6c',
                                    '#f56c6c'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            },
            
            mounted() {
                this.$nextTick(() => {
                    this.initCharts();
                });
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
