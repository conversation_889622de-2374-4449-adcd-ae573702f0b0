<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轻量级威胁建模 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .steps-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .step-content {
            margin-top: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .architecture-upload {
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            padding: 40px;
            text-align: center;
            background: #fafafa;
            transition: border-color 0.3s;
        }

        .architecture-upload:hover {
            border-color: #409eff;
        }

        .upload-icon {
            font-size: 48px;
            color: #c0c4cc;
            margin-bottom: 16px;
        }

        .scenario-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .scenario-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .scenario-card.selected {
            border-color: #409eff;
            background-color: #ecf5ff;
        }

        .scenario-title {
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .scenario-description {
            color: #606266;
            font-size: 14px;
        }

        .threat-item {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .threat-title {
            font-weight: bold;
            color: #cf1322;
            margin-bottom: 4px;
        }

        .threat-description {
            color: #595959;
            font-size: 14px;
        }

        .requirement-item {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .requirement-title {
            font-weight: bold;
            color: #389e0d;
            margin-bottom: 4px;
        }

        .requirement-description {
            color: #595959;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .requirement-source {
            font-size: 12px;
            color: #8c8c8c;
        }

        .progress-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 20px;
        }

        .progress-title {
            font-weight: bold;
            color: #0050b3;
            margin-bottom: 8px;
        }

        .progress-description {
            color: #595959;
            font-size: 14px;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }

        .result-summary {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 轻量级威胁建模
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <div class="page-header">
                    <div class="page-title">轻量级威胁建模</div>
                    <div class="page-subtitle">基于场景化的威胁建模，自动生成安全需求基线和设计方案</div>
                </div>

                <div class="steps-container">
                    <el-steps :active="currentStep" finish-status="success" align-center>
                        <el-step title="问卷调研" description="系统基础信息收集"></el-step>
                        <el-step title="场景分析" description="业务场景识别匹配"></el-step>
                        <el-step title="威胁识别" description="安全威胁自动分析"></el-step>
                        <el-step title="需求生成" description="安全需求基线输出"></el-step>
                        <el-step title="设计方案" description="安全设计方案生成"></el-step>
                    </el-steps>

                    <div class="step-content">
                        <!-- 步骤1: 问卷调研 -->
                        <div v-show="currentStep === 0">
                            <div class="progress-info">
                                <div class="progress-title">问卷调研阶段</div>
                                <div class="progress-description">请填写系统的基础信息，包括系统架构、使用场景、重要数据和部署方式等关键信息。</div>
                            </div>

                            <el-form :model="surveyForm" label-width="120px">
                                <div class="form-section">
                                    <div class="section-title">基本信息</div>
                                    <el-form-item label="项目名称">
                                        <el-input v-model="surveyForm.projectName" placeholder="请输入项目名称"></el-input>
                                    </el-form-item>
                                    <el-form-item label="项目类型">
                                        <el-select v-model="surveyForm.projectType" placeholder="请选择项目类型">
                                            <el-option label="Web应用" value="web"></el-option>
                                            <el-option label="移动应用" value="mobile"></el-option>
                                            <el-option label="API服务" value="api"></el-option>
                                            <el-option label="微服务" value="microservice"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="业务领域">
                                        <el-select v-model="surveyForm.businessDomain" placeholder="请选择业务领域">
                                            <el-option label="证券交易" value="trading"></el-option>
                                            <el-option label="客户服务" value="customer"></el-option>
                                            <el-option label="风险管理" value="risk"></el-option>
                                            <el-option label="内部办公" value="office"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </div>

                                <div class="form-section">
                                    <div class="section-title">系统架构</div>
                                    <el-form-item label="架构图上传">
                                        <div class="architecture-upload" @click="uploadArchitecture">
                                            <el-icon class="upload-icon"><upload-filled /></el-icon>
                                            <div>点击上传系统架构图</div>
                                            <div style="color: #8c8c8c; font-size: 12px; margin-top: 8px;">
                                                支持 PNG、JPG、PDF 格式，大小不超过 10MB
                                            </div>
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="技术栈">
                                        <el-checkbox-group v-model="surveyForm.techStack">
                                            <el-checkbox label="Java">Java</el-checkbox>
                                            <el-checkbox label="Spring Boot">Spring Boot</el-checkbox>
                                            <el-checkbox label="React">React</el-checkbox>
                                            <el-checkbox label="Vue.js">Vue.js</el-checkbox>
                                            <el-checkbox label="MySQL">MySQL</el-checkbox>
                                            <el-checkbox label="Redis">Redis</el-checkbox>
                                            <el-checkbox label="Docker">Docker</el-checkbox>
                                            <el-checkbox label="Kubernetes">Kubernetes</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </div>

                                <div class="form-section">
                                    <div class="section-title">使用场景</div>
                                    <el-form-item label="用户类型">
                                        <el-checkbox-group v-model="surveyForm.userTypes">
                                            <el-checkbox label="external">外部客户</el-checkbox>
                                            <el-checkbox label="internal">内部员工</el-checkbox>
                                            <el-checkbox label="partner">合作伙伴</el-checkbox>
                                            <el-checkbox label="admin">系统管理员</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                    <el-form-item label="访问方式">
                                        <el-checkbox-group v-model="surveyForm.accessMethods">
                                            <el-checkbox label="web">Web浏览器</el-checkbox>
                                            <el-checkbox label="mobile">移动APP</el-checkbox>
                                            <el-checkbox label="api">API接口</el-checkbox>
                                            <el-checkbox label="desktop">桌面应用</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </div>

                                <div class="form-section">
                                    <div class="section-title">数据信息</div>
                                    <el-form-item label="敏感数据类型">
                                        <el-checkbox-group v-model="surveyForm.sensitiveData">
                                            <el-checkbox label="personal">个人身份信息</el-checkbox>
                                            <el-checkbox label="financial">金融交易数据</el-checkbox>
                                            <el-checkbox label="business">商业机密</el-checkbox>
                                            <el-checkbox label="system">系统配置信息</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                    <el-form-item label="数据存储">
                                        <el-input type="textarea" v-model="surveyForm.dataStorage"
                                                placeholder="请描述数据存储方式和位置"></el-input>
                                    </el-form-item>
                                </div>

                                <div class="form-section">
                                    <div class="section-title">部署信息</div>
                                    <el-form-item label="部署环境">
                                        <el-radio-group v-model="surveyForm.deploymentEnv">
                                            <el-radio label="cloud">公有云</el-radio>
                                            <el-radio label="private">私有云</el-radio>
                                            <el-radio label="hybrid">混合云</el-radio>
                                            <el-radio label="onpremise">本地部署</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                    <el-form-item label="网络暴露">
                                        <el-checkbox-group v-model="surveyForm.networkExposure">
                                            <el-checkbox label="internet">互联网</el-checkbox>
                                            <el-checkbox label="intranet">内网</el-checkbox>
                                            <el-checkbox label="dmz">DMZ区域</el-checkbox>
                                            <el-checkbox label="vpn">VPN访问</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>

                        <!-- 步骤2: 场景分析 -->
                        <div v-show="currentStep === 1">
                            <div class="progress-info">
                                <div class="progress-title">场景分析阶段</div>
                                <div class="progress-description">基于您填写的信息，系统自动匹配相关业务场景，请确认或调整适用的场景。</div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">匹配的业务场景</div>
                                <div v-for="scenario in scenarios" :key="scenario.id"
                                     class="scenario-card"
                                     :class="{ selected: selectedScenarios.includes(scenario.id) }"
                                     @click="toggleScenario(scenario.id)">
                                    <div class="scenario-title">{{ scenario.title }}</div>
                                    <div class="scenario-description">{{ scenario.description }}</div>
                                    <div style="margin-top: 8px;">
                                        <el-tag v-for="tag in scenario.tags" :key="tag" size="small" style="margin-right: 8px;">
                                            {{ tag }}
                                        </el-tag>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3: 威胁识别 -->
                        <div v-show="currentStep === 2">
                            <div class="progress-info">
                                <div class="progress-title">威胁识别阶段</div>
                                <div class="progress-description">基于选定的业务场景，系统自动识别潜在的安全威胁。</div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">识别的安全威胁</div>
                                <div v-for="threat in identifiedThreats" :key="threat.id" class="threat-item">
                                    <div class="threat-title">{{ threat.title }}</div>
                                    <div class="threat-description">{{ threat.description }}</div>
                                    <div style="margin-top: 8px;">
                                        <el-tag type="danger" size="small">{{ threat.severity }}</el-tag>
                                        <el-tag type="info" size="small" style="margin-left: 8px;">{{ threat.category }}</el-tag>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4: 需求生成 -->
                        <div v-show="currentStep === 3">
                            <div class="progress-info">
                                <div class="progress-title">安全需求生成阶段</div>
                                <div class="progress-description">基于识别的威胁，自动匹配相应的安全需求和控制措施。</div>
                            </div>

                            <div class="form-section">
                                <div class="section-title">生成的安全需求</div>
                                <div v-for="requirement in securityRequirements" :key="requirement.id" class="requirement-item">
                                    <div class="requirement-title">{{ requirement.title }}</div>
                                    <div class="requirement-description">{{ requirement.description }}</div>
                                    <div class="requirement-source">来源: {{ requirement.source }}</div>
                                    <div style="margin-top: 8px;">
                                        <el-tag type="success" size="small">{{ requirement.priority }}</el-tag>
                                        <el-tag type="info" size="small" style="margin-left: 8px;">{{ requirement.category }}</el-tag>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤5: 设计方案 -->
                        <div v-show="currentStep === 4">
                            <div class="progress-info">
                                <div class="progress-title">安全设计方案阶段</div>
                                <div class="progress-description">基于安全需求，生成具体的安全设计方案和实施建议。</div>
                            </div>

                            <div class="result-summary">
                                <div class="section-title">威胁建模结果摘要</div>
                                <div class="summary-stats">
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #f56c6c;">{{ identifiedThreats.length }}</div>
                                        <div class="stat-label">识别威胁</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #67c23a;">{{ securityRequirements.length }}</div>
                                        <div class="stat-label">安全需求</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #409eff;">{{ selectedScenarios.length }}</div>
                                        <div class="stat-label">适用场景</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number" style="color: #e6a23c;">85%</div>
                                        <div class="stat-label">自动化程度</div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <div class="section-title">安全设计方案</div>
                                    <el-collapse v-model="activeDesignSections">
                                        <el-collapse-item title="身份认证与访问控制" name="auth">
                                            <div>
                                                <p><strong>设计原则：</strong>最小权限原则、职责分离</p>
                                                <p><strong>实施方案：</strong></p>
                                                <ul>
                                                    <li>实施多因子认证(MFA)机制</li>
                                                    <li>建立基于角色的访问控制(RBAC)</li>
                                                    <li>实现单点登录(SSO)集成</li>
                                                    <li>定期进行权限审计和回收</li>
                                                </ul>
                                            </div>
                                        </el-collapse-item>
                                        <el-collapse-item title="数据保护与加密" name="data">
                                            <div>
                                                <p><strong>设计原则：</strong>数据分类分级、加密传输存储</p>
                                                <p><strong>实施方案：</strong></p>
                                                <ul>
                                                    <li>敏感数据字段级加密</li>
                                                    <li>传输层TLS 1.3加密</li>
                                                    <li>数据库透明加密(TDE)</li>
                                                    <li>密钥管理系统(KMS)集成</li>
                                                </ul>
                                            </div>
                                        </el-collapse-item>
                                        <el-collapse-item title="输入验证与输出编码" name="validation">
                                            <div>
                                                <p><strong>设计原则：</strong>白名单验证、输出编码</p>
                                                <p><strong>实施方案：</strong></p>
                                                <ul>
                                                    <li>服务端输入验证框架</li>
                                                    <li>SQL注入防护机制</li>
                                                    <li>XSS防护与CSP策略</li>
                                                    <li>文件上传安全检查</li>
                                                </ul>
                                            </div>
                                        </el-collapse-item>
                                        <el-collapse-item title="日志审计与监控" name="logging">
                                            <div>
                                                <p><strong>设计原则：</strong>全面记录、实时监控</p>
                                                <p><strong>实施方案：</strong></p>
                                                <ul>
                                                    <li>安全事件日志记录</li>
                                                    <li>异常行为实时告警</li>
                                                    <li>日志完整性保护</li>
                                                    <li>合规审计报告生成</li>
                                                </ul>
                                            </div>
                                        </el-collapse-item>
                                    </el-collapse>
                                </div>

                                <div class="action-buttons">
                                    <el-button type="success" size="large" @click="exportReport">
                                        <el-icon><download /></el-icon>
                                        导出威胁建模报告
                                    </el-button>
                                    <el-button type="primary" size="large" @click="startSecurityTesting">
                                        <el-icon><cpu /></el-icon>
                                        启动安全测试
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="action-buttons" v-show="currentStep < 4">
                            <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
                            <el-button type="primary" @click="nextStep" :loading="processing">
                                {{ currentStep === 3 ? '生成设计方案' : '下一步' }}
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    currentStep: 0,
                    processing: false,
                    activeDesignSections: ['auth', 'data'],

                    // 问卷表单数据
                    surveyForm: {
                        projectName: '',
                        projectType: '',
                        businessDomain: '',
                        techStack: [],
                        userTypes: [],
                        accessMethods: [],
                        sensitiveData: [],
                        dataStorage: '',
                        deploymentEnv: '',
                        networkExposure: []
                    },

                    // 业务场景数据
                    scenarios: [
                        {
                            id: 1,
                            title: '证券网上开户',
                            description: '客户通过互联网进行证券账户开户，涉及身份认证、资料上传、视频认证等环节',
                            tags: ['客户服务', 'Web应用', '身份认证']
                        },
                        {
                            id: 2,
                            title: '在线交易系统',
                            description: '客户进行证券买卖交易，涉及资金安全、交易数据保护等',
                            tags: ['交易系统', '资金安全', '实时处理']
                        },
                        {
                            id: 3,
                            title: '移动APP服务',
                            description: '通过移动应用提供证券服务，涉及移动安全、API接口等',
                            tags: ['移动应用', 'API接口', '移动安全']
                        },
                        {
                            id: 4,
                            title: '内部办公系统',
                            description: '员工日常办公使用的内部系统，涉及内网安全、权限管理等',
                            tags: ['内部系统', '权限管理', '内网安全']
                        }
                    ],

                    selectedScenarios: [],

                    // 威胁数据
                    identifiedThreats: [
                        {
                            id: 1,
                            title: '身份伪造攻击',
                            description: '攻击者可能通过伪造身份信息进行恶意注册或登录',
                            severity: '高危',
                            category: '身份认证'
                        },
                        {
                            id: 2,
                            title: 'SQL注入攻击',
                            description: '恶意用户通过构造SQL语句获取或篡改数据库数据',
                            severity: '高危',
                            category: '输入验证'
                        },
                        {
                            id: 3,
                            title: '敏感数据泄露',
                            description: '客户个人信息或交易数据可能在传输或存储过程中泄露',
                            severity: '中危',
                            category: '数据保护'
                        },
                        {
                            id: 4,
                            title: '会话劫持',
                            description: '攻击者可能劫持用户会话进行未授权操作',
                            severity: '中危',
                            category: '会话管理'
                        }
                    ],

                    // 安全需求数据
                    securityRequirements: [
                        {
                            id: 1,
                            title: '多因子身份认证',
                            description: '系统应实施多因子认证机制，包括密码、短信验证码、生物特征等',
                            source: '《证券公司网上证券信息系统技术指引》',
                            priority: '高优先级',
                            category: '身份认证'
                        },
                        {
                            id: 2,
                            title: '输入数据验证',
                            description: '所有用户输入数据必须进行严格的格式和内容验证',
                            source: 'OWASP ASVS',
                            priority: '高优先级',
                            category: '输入验证'
                        },
                        {
                            id: 3,
                            title: '敏感数据加密',
                            description: '个人身份信息和交易数据必须进行加密存储和传输',
                            source: '《个人信息保护法》',
                            priority: '高优先级',
                            category: '数据保护'
                        },
                        {
                            id: 4,
                            title: '安全审计日志',
                            description: '系统应记录所有安全相关事件的详细日志',
                            source: '《网络安全法》',
                            priority: '中优先级',
                            category: '审计监控'
                        }
                    ]
                };
            },

            methods: {
                nextStep() {
                    if (this.currentStep === 0) {
                        // 验证问卷表单
                        if (!this.surveyForm.projectName || !this.surveyForm.projectType) {
                            ElMessage.warning('请填写完整的基本信息');
                            return;
                        }
                    }

                    if (this.currentStep === 1) {
                        // 验证场景选择
                        if (this.selectedScenarios.length === 0) {
                            ElMessage.warning('请至少选择一个业务场景');
                            return;
                        }
                    }

                    this.processing = true;

                    // 模拟处理时间
                    setTimeout(() => {
                        this.currentStep++;
                        this.processing = false;

                        if (this.currentStep === 2) {
                            ElMessage.success('威胁识别完成，发现 ' + this.identifiedThreats.length + ' 个潜在威胁');
                        } else if (this.currentStep === 3) {
                            ElMessage.success('安全需求生成完成，匹配 ' + this.securityRequirements.length + ' 个安全需求');
                        } else if (this.currentStep === 4) {
                            ElMessage.success('安全设计方案生成完成');
                        }
                    }, 1500);
                },

                prevStep() {
                    if (this.currentStep > 0) {
                        this.currentStep--;
                    }
                },

                toggleScenario(scenarioId) {
                    const index = this.selectedScenarios.indexOf(scenarioId);
                    if (index > -1) {
                        this.selectedScenarios.splice(index, 1);
                    } else {
                        this.selectedScenarios.push(scenarioId);
                    }
                },

                uploadArchitecture() {
                    ElMessage.info('架构图上传功能开发中...');
                },

                exportReport() {
                    ElMessage.success('威胁建模报告导出成功');
                },

                startSecurityTesting() {
                    ElMessageBox.confirm(
                        '是否启动自动化安全测试？这将根据威胁建模结果配置相应的安全扫描策略。',
                        '启动安全测试',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        ElMessage.success('安全测试任务已创建，正在跳转到ASOC测试编排模块...');
                        setTimeout(() => {
                            window.location.href = 'asoc-testing.html';
                        }, 1500);
                    });
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            },

            mounted() {
                // 模拟自动选择场景
                setTimeout(() => {
                    this.selectedScenarios = [1, 2];
                }, 1000);
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>