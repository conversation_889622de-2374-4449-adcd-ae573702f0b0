<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .main-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .project-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .project-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .project-name {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }

        .project-info {
            margin-bottom: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            color: #606266;
        }

        .progress-section {
            margin-bottom: 16px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            color: #606266;
        }

        .team-avatars {
            display: flex;
            margin-bottom: 16px;
        }

        .team-avatar {
            margin-right: -8px;
            border: 2px solid white;
        }

        .project-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .milestone-item {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .milestone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .milestone-name {
            font-weight: bold;
            color: #303133;
        }

        .milestone-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .milestone-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #909399;
        }

        .task-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .task-name {
            font-weight: bold;
            color: #303133;
        }

        .task-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #909399;
        }

        .gantt-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .gantt-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .gantt-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }

        .gantt-chart {
            overflow-x: auto;
        }

        .gantt-row {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .gantt-task-name {
            width: 200px;
            font-size: 14px;
            color: #303133;
            padding-right: 16px;
        }

        .gantt-timeline {
            flex: 1;
            height: 24px;
            background: #f5f7fa;
            border-radius: 4px;
            position: relative;
        }

        .gantt-bar {
            height: 100%;
            background: #409eff;
            border-radius: 4px;
            position: absolute;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e4e7ed;
        }

        .search-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 项目管理
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>项目概览</span>
                        </el-menu-item>
                        <el-menu-item index="projects">
                            <el-icon><folder /></el-icon>
                            <span>项目列表</span>
                        </el-menu-item>
                        <el-menu-item index="milestones">
                            <el-icon><flag /></el-icon>
                            <span>里程碑管理</span>
                        </el-menu-item>
                        <el-menu-item index="tasks">
                            <el-icon><list /></el-icon>
                            <span>任务管理</span>
                        </el-menu-item>
                        <el-menu-item index="gantt">
                            <el-icon><data-line /></el-icon>
                            <span>甘特图</span>
                        </el-menu-item>
                        <el-menu-item index="team">
                            <el-icon><user /></el-icon>
                            <span>团队协作</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 主内容区域 -->
                <div class="content-area">
                    <!-- 项目概览 -->
                    <div v-show="currentPage === 'dashboard'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">项目管理概览</div>
                                <div class="page-subtitle">统一管理项目进度，协调团队协作</div>
                            </div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #409eff;">{{ totalProjects }}</div>
                                <div class="stat-label">项目总数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #67c23a;">{{ activeProjects }}</div>
                                <div class="stat-label">进行中项目</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #e6a23c;">{{ delayedProjects }}</div>
                                <div class="stat-label">延期项目</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #f56c6c;">{{ completedProjects }}</div>
                                <div class="stat-label">已完成项目</div>
                            </div>
                        </div>

                        <div class="main-content">
                            <div class="section-title">项目进度概览</div>
                            <div class="project-grid">
                                <div v-for="project in projects.slice(0, 6)" :key="project.id" class="project-card" @click="viewProjectDetail(project)">
                                    <div class="project-header">
                                        <div class="project-name">{{ project.name }}</div>
                                        <el-tag :type="getStatusType(project.status)" size="small">
                                            {{ project.status }}
                                        </el-tag>
                                    </div>

                                    <div class="project-info">
                                        <div class="info-row">
                                            <span>负责人:</span>
                                            <span>{{ project.manager }}</span>
                                        </div>
                                        <div class="info-row">
                                            <span>开始时间:</span>
                                            <span>{{ project.startDate }}</span>
                                        </div>
                                        <div class="info-row">
                                            <span>预计完成:</span>
                                            <span>{{ project.endDate }}</span>
                                        </div>
                                    </div>

                                    <div class="progress-section">
                                        <div class="progress-label">
                                            <span>进度</span>
                                            <span>{{ project.progress }}%</span>
                                        </div>
                                        <el-progress :percentage="project.progress" :color="getProgressColor(project.progress)"></el-progress>
                                    </div>

                                    <div class="team-avatars">
                                        <el-avatar v-for="member in project.team.slice(0, 4)" :key="member.id"
                                                 :size="32" :src="member.avatar" class="team-avatar">
                                            {{ member.name.charAt(0) }}
                                        </el-avatar>
                                        <el-avatar v-if="project.team.length > 4" :size="32" class="team-avatar">
                                            +{{ project.team.length - 4 }}
                                        </el-avatar>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 项目列表 -->
                    <div v-show="currentPage === 'projects'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">项目列表</div>
                                <div class="page-subtitle">管理所有项目的详细信息</div>
                            </div>
                            <el-button type="primary" @click="showCreateProjectDialog = true">
                                <el-icon><plus /></el-icon>
                                新建项目
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="toolbar">
                                <div class="search-bar">
                                    <el-input
                                        v-model="searchKeyword"
                                        placeholder="搜索项目名称"
                                        style="width: 300px;"
                                        clearable>
                                        <template #prefix>
                                            <el-icon><search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="进行中" value="active"></el-option>
                                        <el-option label="已完成" value="completed"></el-option>
                                        <el-option label="已延期" value="delayed"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button @click="exportProjects">
                                        <el-icon><download /></el-icon>
                                        导出
                                    </el-button>
                                </div>
                            </div>

                            <div class="project-grid">
                                <div v-for="project in filteredProjects" :key="project.id" class="project-card" @click="viewProjectDetail(project)">
                                    <div class="project-header">
                                        <div class="project-name">{{ project.name }}</div>
                                        <el-tag :type="getStatusType(project.status)" size="small">
                                            {{ project.status }}
                                        </el-tag>
                                    </div>

                                    <div class="project-info">
                                        <div class="info-row">
                                            <span>负责人:</span>
                                            <span>{{ project.manager }}</span>
                                        </div>
                                        <div class="info-row">
                                            <span>团队规模:</span>
                                            <span>{{ project.team.length }}人</span>
                                        </div>
                                        <div class="info-row">
                                            <span>开始时间:</span>
                                            <span>{{ project.startDate }}</span>
                                        </div>
                                        <div class="info-row">
                                            <span>预计完成:</span>
                                            <span>{{ project.endDate }}</span>
                                        </div>
                                    </div>

                                    <div class="progress-section">
                                        <div class="progress-label">
                                            <span>整体进度</span>
                                            <span>{{ project.progress }}%</span>
                                        </div>
                                        <el-progress :percentage="project.progress" :color="getProgressColor(project.progress)"></el-progress>
                                    </div>

                                    <div class="team-avatars">
                                        <el-avatar v-for="member in project.team.slice(0, 5)" :key="member.id"
                                                 :size="32" :src="member.avatar" class="team-avatar">
                                            {{ member.name.charAt(0) }}
                                        </el-avatar>
                                        <el-avatar v-if="project.team.length > 5" :size="32" class="team-avatar">
                                            +{{ project.team.length - 5 }}
                                        </el-avatar>
                                    </div>

                                    <div class="project-actions" @click.stop>
                                        <el-button size="small" type="primary" @click="editProject(project)">编辑</el-button>
                                        <el-button size="small" @click="viewTasks(project)">任务</el-button>
                                        <el-button size="small" @click="viewMilestones(project)">里程碑</el-button>
                                        <el-dropdown>
                                            <el-button size="small">
                                                更多<el-icon><arrow-down /></el-icon>
                                            </el-button>
                                            <template #dropdown>
                                                <el-dropdown-menu>
                                                    <el-dropdown-item @click="generateProjectReport(project)">生成报告</el-dropdown-item>
                                                    <el-dropdown-item @click="archiveProject(project)">归档项目</el-dropdown-item>
                                                    <el-dropdown-item divided @click="deleteProject(project)">删除项目</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 甘特图 -->
                    <div v-show="currentPage === 'gantt'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">项目甘特图</div>
                                <div class="page-subtitle">可视化项目时间线和任务依赖关系</div>
                            </div>
                        </div>

                        <div class="gantt-container">
                            <div class="gantt-header">
                                <div class="gantt-title">项目时间线</div>
                                <el-select v-model="selectedProjectId" placeholder="选择项目" style="width: 200px;">
                                    <el-option v-for="project in projects" :key="project.id"
                                             :label="project.name" :value="project.id"></el-option>
                                </el-select>
                            </div>

                            <div class="gantt-chart" v-if="selectedProject">
                                <div v-for="task in selectedProject.tasks" :key="task.id" class="gantt-row">
                                    <div class="gantt-task-name">{{ task.name }}</div>
                                    <div class="gantt-timeline">
                                        <div class="gantt-bar"
                                             :style="{
                                                 left: task.startPercent + '%',
                                                 width: task.durationPercent + '%',
                                                 backgroundColor: getTaskColor(task.status)
                                             }"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 里程碑管理 -->
                    <div v-show="currentPage === 'milestones'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">里程碑管理</div>
                                <div class="page-subtitle">管理项目关键节点和交付物</div>
                            </div>
                            <el-button type="primary" @click="showCreateMilestoneDialog = true">
                                <el-icon><plus /></el-icon>
                                新建里程碑
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div v-for="milestone in milestones" :key="milestone.id" class="milestone-item">
                                <div class="milestone-header">
                                    <div class="milestone-name">{{ milestone.name }}</div>
                                    <div>
                                        <el-tag :type="getMilestoneStatusType(milestone.status)" size="small">
                                            {{ milestone.status }}
                                        </el-tag>
                                        <el-tag type="info" size="small" style="margin-left: 8px;">
                                            {{ milestone.project }}
                                        </el-tag>
                                    </div>
                                </div>
                                <div class="milestone-description">{{ milestone.description }}</div>
                                <div class="milestone-meta">
                                    <span>负责人: {{ milestone.owner }}</span>
                                    <span>截止时间: {{ milestone.dueDate }}</span>
                                    <span>完成度: {{ milestone.progress }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务管理 -->
                    <div v-show="currentPage === 'tasks'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">任务管理</div>
                                <div class="page-subtitle">管理项目中的具体任务和分工</div>
                            </div>
                            <el-button type="primary" @click="showCreateTaskDialog = true">
                                <el-icon><plus /></el-icon>
                                新建任务
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="toolbar">
                                <div class="search-bar">
                                    <el-input
                                        v-model="taskSearchKeyword"
                                        placeholder="搜索任务"
                                        style="width: 300px;"
                                        clearable>
                                        <template #prefix>
                                            <el-icon><search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="taskStatusFilter" placeholder="状态筛选" style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="待开始" value="pending"></el-option>
                                        <el-option label="进行中" value="active"></el-option>
                                        <el-option label="已完成" value="completed"></el-option>
                                    </el-select>
                                    <el-select v-model="taskProjectFilter" placeholder="项目筛选" style="width: 150px;">
                                        <el-option label="全部项目" value="all"></el-option>
                                        <el-option v-for="project in projects" :key="project.id"
                                                 :label="project.name" :value="project.name"></el-option>
                                    </el-select>
                                </div>
                            </div>

                            <div v-for="task in filteredTasks" :key="task.id" class="task-item">
                                <div class="task-header">
                                    <div class="task-name">{{ task.name }}</div>
                                    <div>
                                        <el-tag :type="getTaskStatusType(task.status)" size="small">
                                            {{ task.status }}
                                        </el-tag>
                                        <el-tag :type="getPriorityType(task.priority)" size="small" style="margin-left: 8px;">
                                            {{ task.priority }}
                                        </el-tag>
                                    </div>
                                </div>
                                <div class="task-description">{{ task.description }}</div>
                                <div class="task-meta">
                                    <div>
                                        <span>项目: {{ task.project }}</span>
                                        <span style="margin-left: 16px;">负责人: {{ task.assignee }}</span>
                                        <span style="margin-left: 16px;">截止时间: {{ task.dueDate }}</span>
                                    </div>
                                    <div>
                                        <el-button size="small" type="primary" @click="editTask(task)">编辑</el-button>
                                        <el-button size="small" @click="completeTask(task)">完成</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    activeMenu: 'dashboard',
                    currentPage: 'dashboard',
                    searchKeyword: '',
                    statusFilter: 'all',
                    taskSearchKeyword: '',
                    taskStatusFilter: 'all',
                    taskProjectFilter: 'all',
                    selectedProjectId: null,
                    showCreateProjectDialog: false,
                    showCreateMilestoneDialog: false,
                    showCreateTaskDialog: false,

                    // 统计数据
                    totalProjects: 24,
                    activeProjects: 18,
                    delayedProjects: 3,
                    completedProjects: 3,

                    // 项目数据
                    projects: [
                        {
                            id: 1,
                            name: '证券交易系统升级',
                            status: '进行中',
                            manager: '李四',
                            startDate: '2024-01-01',
                            endDate: '2024-06-30',
                            progress: 65,
                            team: [
                                { id: 1, name: '张三', avatar: '', role: '前端开发' },
                                { id: 2, name: '李四', avatar: '', role: '后端开发' },
                                { id: 3, name: '王五', avatar: '', role: '测试工程师' },
                                { id: 4, name: '赵六', avatar: '', role: 'UI设计师' },
                                { id: 5, name: '钱七', avatar: '', role: '产品经理' }
                            ],
                            tasks: [
                                { id: 1, name: '需求分析', status: '已完成', startPercent: 0, durationPercent: 15 },
                                { id: 2, name: '系统设计', status: '已完成', startPercent: 15, durationPercent: 20 },
                                { id: 3, name: '前端开发', status: '进行中', startPercent: 35, durationPercent: 25 },
                                { id: 4, name: '后端开发', status: '进行中', startPercent: 30, durationPercent: 30 },
                                { id: 5, name: '系统测试', status: '待开始', startPercent: 60, durationPercent: 20 },
                                { id: 6, name: '上线部署', status: '待开始', startPercent: 80, durationPercent: 20 }
                            ]
                        },
                        {
                            id: 2,
                            name: '移动APP开发',
                            status: '进行中',
                            manager: '王五',
                            startDate: '2024-02-15',
                            endDate: '2024-08-15',
                            progress: 45,
                            team: [
                                { id: 6, name: '孙八', avatar: '', role: 'iOS开发' },
                                { id: 7, name: '周九', avatar: '', role: 'Android开发' },
                                { id: 8, name: '吴十', avatar: '', role: '测试工程师' }
                            ],
                            tasks: [
                                { id: 7, name: 'UI设计', status: '已完成', startPercent: 0, durationPercent: 20 },
                                { id: 8, name: 'iOS开发', status: '进行中', startPercent: 20, durationPercent: 35 },
                                { id: 9, name: 'Android开发', status: '进行中', startPercent: 20, durationPercent: 35 },
                                { id: 10, name: '功能测试', status: '待开始', startPercent: 55, durationPercent: 25 },
                                { id: 11, name: '发布上线', status: '待开始', startPercent: 80, durationPercent: 20 }
                            ]
                        },
                        {
                            id: 3,
                            name: '数据安全合规项目',
                            status: '已完成',
                            manager: '张三',
                            startDate: '2023-10-01',
                            endDate: '2024-01-31',
                            progress: 100,
                            team: [
                                { id: 9, name: '李明', avatar: '', role: '安全专家' },
                                { id: 10, name: '陈红', avatar: '', role: '合规专员' }
                            ],
                            tasks: [
                                { id: 12, name: '合规评估', status: '已完成', startPercent: 0, durationPercent: 30 },
                                { id: 13, name: '安全加固', status: '已完成', startPercent: 30, durationPercent: 40 },
                                { id: 14, name: '验收测试', status: '已完成', startPercent: 70, durationPercent: 30 }
                            ]
                        }
                    ],

                    // 里程碑数据
                    milestones: [
                        {
                            id: 1,
                            name: '系统设计完成',
                            description: '完成系统架构设计和技术方案评审',
                            project: '证券交易系统升级',
                            owner: '李四',
                            dueDate: '2024-02-29',
                            status: '已完成',
                            progress: 100
                        },
                        {
                            id: 2,
                            name: '核心功能开发完成',
                            description: '完成交易、清算、风控等核心功能开发',
                            project: '证券交易系统升级',
                            owner: '张三',
                            dueDate: '2024-04-30',
                            status: '进行中',
                            progress: 75
                        },
                        {
                            id: 3,
                            name: 'APP Beta版发布',
                            description: '发布移动APP的Beta测试版本',
                            project: '移动APP开发',
                            owner: '王五',
                            dueDate: '2024-06-15',
                            status: '待开始',
                            progress: 0
                        }
                    ],

                    // 任务数据
                    tasks: [
                        {
                            id: 1,
                            name: '用户界面重构',
                            description: '重新设计交易界面，提升用户体验',
                            project: '证券交易系统升级',
                            assignee: '张三',
                            status: '进行中',
                            priority: '高',
                            dueDate: '2024-03-15'
                        },
                        {
                            id: 2,
                            name: 'API接口开发',
                            description: '开发移动端所需的REST API接口',
                            project: '移动APP开发',
                            assignee: '李四',
                            status: '待开始',
                            priority: '中',
                            dueDate: '2024-04-01'
                        },
                        {
                            id: 3,
                            name: '安全漏洞修复',
                            description: '修复安全扫描发现的高危漏洞',
                            project: '证券交易系统升级',
                            assignee: '王五',
                            status: '已完成',
                            priority: '高',
                            dueDate: '2024-02-20'
                        },
                        {
                            id: 4,
                            name: '性能优化',
                            description: '优化系统性能，提升响应速度',
                            project: '证券交易系统升级',
                            assignee: '赵六',
                            status: '进行中',
                            priority: '中',
                            dueDate: '2024-03-30'
                        }
                    ]
                };
            },

            computed: {
                filteredProjects() {
                    let filtered = this.projects;

                    if (this.searchKeyword) {
                        filtered = filtered.filter(project =>
                            project.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
                        );
                    }

                    if (this.statusFilter !== 'all') {
                        filtered = filtered.filter(project => {
                            if (this.statusFilter === 'active') return project.status === '进行中';
                            if (this.statusFilter === 'completed') return project.status === '已完成';
                            if (this.statusFilter === 'delayed') return project.status === '已延期';
                            return true;
                        });
                    }

                    return filtered;
                },

                filteredTasks() {
                    let filtered = this.tasks;

                    if (this.taskSearchKeyword) {
                        filtered = filtered.filter(task =>
                            task.name.toLowerCase().includes(this.taskSearchKeyword.toLowerCase())
                        );
                    }

                    if (this.taskStatusFilter !== 'all') {
                        filtered = filtered.filter(task => {
                            if (this.taskStatusFilter === 'pending') return task.status === '待开始';
                            if (this.taskStatusFilter === 'active') return task.status === '进行中';
                            if (this.taskStatusFilter === 'completed') return task.status === '已完成';
                            return true;
                        });
                    }

                    if (this.taskProjectFilter !== 'all') {
                        filtered = filtered.filter(task => task.project === this.taskProjectFilter);
                    }

                    return filtered;
                },

                selectedProject() {
                    return this.projects.find(p => p.id === this.selectedProjectId);
                }
            },

            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },

                getStatusType(status) {
                    switch (status) {
                        case '进行中': return 'success';
                        case '已完成': return 'info';
                        case '已延期': return 'danger';
                        default: return 'warning';
                    }
                },

                getProgressColor(percentage) {
                    if (percentage < 30) return '#f56c6c';
                    if (percentage < 70) return '#e6a23c';
                    return '#67c23a';
                },

                getTaskColor(status) {
                    switch (status) {
                        case '已完成': return '#67c23a';
                        case '进行中': return '#409eff';
                        case '待开始': return '#e6a23c';
                        default: return '#909399';
                    }
                },

                getMilestoneStatusType(status) {
                    switch (status) {
                        case '已完成': return 'success';
                        case '进行中': return 'warning';
                        case '待开始': return 'info';
                        default: return 'info';
                    }
                },

                getTaskStatusType(status) {
                    switch (status) {
                        case '已完成': return 'success';
                        case '进行中': return 'warning';
                        case '待开始': return 'info';
                        default: return 'info';
                    }
                },

                getPriorityType(priority) {
                    switch (priority) {
                        case '高': return 'danger';
                        case '中': return 'warning';
                        case '低': return 'info';
                        default: return 'info';
                    }
                },

                viewProjectDetail(project) {
                    ElMessage.info(`查看项目"${project.name}"详情功能开发中...`);
                },

                editProject(project) {
                    ElMessage.info(`编辑项目"${project.name}"功能开发中...`);
                },

                viewTasks(project) {
                    this.taskProjectFilter = project.name;
                    this.activeMenu = 'tasks';
                    this.currentPage = 'tasks';
                },

                viewMilestones(project) {
                    this.activeMenu = 'milestones';
                    this.currentPage = 'milestones';
                },

                generateProjectReport(project) {
                    ElMessage.success(`正在生成项目"${project.name}"的报告...`);
                },

                archiveProject(project) {
                    ElMessageBox.confirm(
                        `确定要归档项目"${project.name}"吗？`,
                        '归档项目',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        ElMessage.success('项目归档成功');
                    });
                },

                deleteProject(project) {
                    ElMessageBox.confirm(
                        `确定要删除项目"${project.name}"吗？此操作不可恢复。`,
                        '删除项目',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        const index = this.projects.findIndex(p => p.id === project.id);
                        if (index > -1) {
                            this.projects.splice(index, 1);
                            ElMessage.success('项目删除成功');
                        }
                    });
                },

                editTask(task) {
                    ElMessage.info(`编辑任务"${task.name}"功能开发中...`);
                },

                completeTask(task) {
                    task.status = '已完成';
                    ElMessage.success(`任务"${task.name}"已标记为完成`);
                },

                exportProjects() {
                    ElMessage.success('项目列表导出成功');
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            },

            mounted() {
                // 默认选择第一个项目用于甘特图显示
                if (this.projects.length > 0) {
                    this.selectedProjectId = this.projects[0].id;
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
