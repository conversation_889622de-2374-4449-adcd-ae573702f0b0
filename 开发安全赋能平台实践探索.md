# 开发安全赋能平台实践探索

**摘要**：随着攻击手段的复杂化和法律合规要求趋严，安全左移已经成为行业共识，可以显著降低修复成本及提高产品安全性。伴随软件开发模式的敏捷化转变和DevOps的流行，DevSecOps相应出现，但机构在落地过程中，普遍面临诸多困难，如安全人员不足、缺少威胁分析工具和安全需求库等，严重影响了应用系统的安全保障效果。本文将介绍开发安全赋能平台的实践探索，以平台为载体整合和重构安全专家多年积累的领域知识、经验、技术、工具和流程，将安全赋能到软件开发的各个阶段，伴随研发流程进行安全风险同步管理，通过自助化、流程化、自动化的平台赋能，减轻机构从事DevSecOps的困难，提高安全效能，快速交付合法、合规，且满足较高安全保障水平的软件产品。



**关键词**：开发安全赋能平台、DevSecOps、轻量级威胁建模、安全测试编排与关联、数据安全评估内嵌、开发安全度量指标



***\*01前言
\****

随着网络安全威胁日益复杂、攻击方式的快速迭代、法律合规要求的提升以及云原生等技术架构的演进，安全左移不仅是响应这些变化的有效手段，更是现代网络安全战略中不可或缺的环节。通过将安全工作前置，企业能够更高效地预防漏洞，适应未来的安全需求。在软件开发过程的各个阶段，融入安全风险管理，是当前公认有效的应用安全保障方法。实施软件生命周期安全风险管理，由简单的补丁管理思想向全面的软件安全保障体系转变，在软件开发各阶段嵌入安全要素，避免或尽早发现、整改可能的缺陷和漏洞，识别和遵从法律法规和内外部监管要求，以经济、高效的方式提升软件产品的内生安全性。这也是行业对于信息系统“相关安全技术措施与信息化工作同步规划、同步建设、同步使用”的明确要求。



伴随软件开发模式的敏捷化转变和DevOps的流行，DevSecOps概念相应出现，以安全左移为指引，集成于DevOps的流程和工具体系中，从软件开发早期入手，覆盖开发各阶段，避免和减少各类缺陷和漏洞的出现，降低修复成本。



在实践SDL软件安全开发生命周期或DevSecOps时，行业机构普遍面临着诸多困境，如安全专业人员不足、开发人员安全意识与技能薄弱、安全测试工具分散且自动化不足和准确性不高等，严重影响了应用系统的安全保障效果，迫切需要一套以安全专家领域知识、经验为基础，集成软件安全保障技术、工具和流程为一体的软件开发安全建设辅助系统，实现和提高软件系统在应用资产管理、安全需求分析、安全设计、安全实现、安全部署、安全验证和风险评估等阶段和环节的专业化、自动化、工具化和标准化。



本文对开发安全赋能平台进行探索，以平台为载体整合和重构安全专家多年积累的领域知识、经验、技术、工具和流程，将安全赋能到软件开发的需求、设计、编码、测试及部署各个阶段，伴随研发流程进行安全风险同步管理，通过自助化、基线化、流程化及自动化的平台赋能，减轻机构从事SDL/DevSecOps的困难，提高安全效能，快速交付合法、合规，且满足较高安全保障水平的软件产品。





***\*02 关键能力
\****

开发安全赋能平台，目的是基于平台给开发赋能，通过技术、工具和流程，将安全能力融入软件开发生命周期各个阶段，帮助开发团队识别和降低安全风险，同时提升团队的安全意识和效能。开发安全赋能平台应该具备如下关键能力：



**2.1** **研发过程安全活动****内嵌**

开发安全赋能平台，将研发过程及安全活动有效的集成到一起，通过平台，在软件研发过程同步开展安全风险管控。以开发安全赋能平台为载体，在研发过程集成的安全活动包括：

计划阶段：安全培训、轻量级威胁建模。

编码阶段：安全编码规范、场景化安全编码案例库。

构建阶段：SAST源代码安全扫描、SCA开源组件安全扫描、移动APP加固、容器镜像安全扫描。

测试阶段：IAST交互式安全测试、DAST黑盒安全扫描、MAST移动安全测试、渗透测试。

部署阶段：生产环境部署验证（安全套件安装检测、主机安全扫描、web中间件等安全基线检测）。



通过开发安全赋能平台，实现研发流程安全活动过程管控，实现SDL/DevSecOps流程、方法、工具及知识库在软件开发全生命周期各个阶段的集成，协助组织打造自己内生安全体系。



在应用系统建设过程中，将安全培训、安全需求、安全实现、安全测试及安全部署等安全活动工程化、自助化、基线化和自动化，简化开发人员参与安全流程的复杂度，降低开发人员的安全技能依赖度，提高安全参与度，从而降低人力成本，提升应用安全运营效能，在不影响产品交付速度的前提下，构建安全合规的产品。



**2****.2** **安全需求库**

**
**

行业内应用系统都会面临各种不同类型、不同程度的安全风险，包括：法律法规风险、合规监管风险、内容风险、个人隐私和数据安全风险、WEB应用、移动应用和其它新技术应用风险等。通过持续的风险分析活动，可以不断收集、形成、完善安全需求库，以便在具体应用系统建设初期，对照和形成有针对性的安全建设需求列表，做为系统后期详细设计、编码、测试、部署、运行的基础。从而有效地降低应用系统上线后的安全风险，提升应用系统的安全保障能力。



行业普遍适用和关注的应用安全和数据安全需求来源包括四个层面，一是法律法规/国家标准,如《网络安全法》、《数据安全法》、《个人隐私保护法》、《关键基础设施系统保护条例》、《信息系统安全等级保护基本要求》等；二是行业监管法令、指引及技术标准等，如《证券公司网上证券信息系统技术指引》、《证券期货业移动互联网应用程序安全规范》等，通常具有行业强制性，违规成本高；三是公司自有信息安全策略、标准及指南等，以维护公司利益为出发点。四是业界安全最佳实践，如OWASP ASVS、MITRE CVE、CWE、CAPEC等。



将各来源的需求，经过分类、去重后，按统一的方式，编号，入库，确定对应的信息系统场景（即适用条件），形成安全需求库，以便进行自动化的，基于场景的轻量级安全需求分析和威胁建模。



**2****.3** **轻量级威胁建模**

**
**

威胁建模（threat modeling）是一个主动过程，通过对资产的可能攻击行为的识别，针对性地事先构建出威胁分析应用程序安全性的一种结构化方法，通过识别威胁，定义防御或消除威胁控制措施的一个主动过程。



传统的威胁建模技术包括STRIDE、PASTA、OCTAVE等。传统威胁建模技术中，往往还会辅助采用 NIST开发的CVSS和攻击树分析方法，以形成更加完整的威胁分析与评价系统。



传统的威胁建模流程太重，过于繁琐，对于人工投入要求较高，很难适应业务的敏捷快速迭代开发模式，在实际研发过程中很难落地。对威胁建模过程进行优化，将安全需求分析和架构设计过程结合，实现基于场景化的轻量级威胁建模方法，主要步骤如下：



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIicqQtIWdLwsibLBia4BCbyozRia5PbxNMDQhOhicHfpEicmCYOjqDibdkTVCg/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



**1）****问卷调研**。了解系统的关键信息，为后面的攻击面分析及威胁建模做准备。调研问卷主要包括系统架构、使用场景、重要数据、部署方式等。其中系统架构关注统架构图、技术实现方案等，使用场景关注用户场景、用户群体、角色、访问方式等，数据关注是否有敏感数据，而部署关注部署架构及物理资产。



**2****）****场景分析及通用场景库**。根据金融应用软件的部分共性特征和证券业应用部分独有特征，总结相关的应用场景。根据用户区分为对客和自有员工，对客场景又划分为业务场景、应用场景和部署场景，自有员工场景划分为办公场景和业务系统场景。



**3）****安全威胁库**。安全威胁库与业务场景关系密切，通常情况下需要通过问卷调研了解系统的关键信息，匹配对应的业务场景，对攻击面分析及威胁进行分析，识别典型的安全威胁，形成业务场景对应的安全威胁库。



以证券网上开户为例进行威胁分析，证券开户业务涉及到账号注册、身份认证、资料上传、视频认证、密码找回等多个环节，识别的安全威胁如下：

![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIEic2GqtEia3BIbxt8tHXgzjZhHEMiasrLprIlsmuHRlACbH6ngYHRSuMg/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



**4****）安全需求库**。确定了业务场景，识别系统潜在的安全威胁后，需要进行风险缓释，给项目组提出相应的安全需求。通过构建证券行业安全需求库，实现安全威胁库中的风险都有对应的安全需求及针对性解决方案(安全需求库的建设见2.1)。



**5）安全需求基线**。基于业务场景和安全威胁库，再匹配对应的安全需求库，既可以输出系统需要满足安全需求基线。匹配方式在开发安全赋能平台中基于规则算法进行自动化匹配。



![img]()**6****）安全设计方案**。安全需求的实现，安全团队需要提供相应的安全设计方案。安全设计应遵循安全设计原则，如开放设计、权限分离、最小权限、经济适用、完全仲裁、心理可承受等原则。



![图片](data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='1px' height='1px' viewBox='0 0 1 1' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3E%3C/title%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' fill-opacity='0'%3E%3Cg transform='translate(-249.000000, -126.000000)' fill='%23FFFFFF'%3E%3Crect x='249' y='126' width='1' height='1'%3E%3C/rect%3E%3C/g%3E%3C/g%3E%3C/svg%3E)



研发和系统设计人员根据安全设计方案，在系统建设过程中融入安全设计方案，实现通过设计保证安全。

如上即是基于业务场景化轻量级威胁建模方法的全过程，通过平台实现自动化轻量级威胁建模具体落地。



**2.4** **ASOC****应用安全测试编排和关联**

**
**

ASOC(“Application Security Orchestration and Correlation”应用安全编排与关联)，结合了ASTO（应用安全测试编排）和AVC（应用安全漏洞相关性）两个方向。ASOC的核心能力是这两个方向核心能力的并集，向上统一管控各类开发安全工具，向下统一对接DevOps各类安全需求，并提供自流程编排能力与工具编排能力，将流程与数据统一管理、非同源安全漏洞的关联分析，重新对漏洞危险等级排序，提供贴合企业业务场景的漏洞修复细节等。



开发安全赋能平台应该具备ASOC能力，主要包含如下功能：



**1）****扫描工具引擎化编排****ASTO（应用安全测试编排）****能力**。开发安全赋能平台将各类安全扫描工具集成做统一化管理，将各个安全扫描工具设计成众多可插拔独立引擎，由平台统一编排，可自由组合引擎执行扫描任务剧本。



**2）****扫描策略剧本编排能力**。剧本编排在平台中是重要的事件驱动机制，编排剧本能力可以更好的解决不同场景的安全需求。剧本编排必须做到以下几点：剧本场景的选择、剧本执行时间、使用到的扫描引擎组合、剧本执行优先级、可选择的报告模板（内部使用/外部使用）。



**3）安全测试****集群可视化管理**。平台将各个安全扫描工具集群集中统一化管理，在平台中需要呈现出：各个扫描工具扫描节点任务状态、机器健康状态、闲置节点数，忙碌节点数、各类工具扫描度量数值、扫描节点指定场景使用分配等，并且能够在集群资源紧缺时给出应急告警。



**4）****漏洞统一化中转能力**。相关漏洞信息字段由ASOC指定，如漏洞简述、风险等级、漏洞详情、发现时间、修复意见、漏洞来源等，将各扫描工具不同扫描结果统一由平台做统一处理，中转成统一漏洞信息字段。



**5）****漏洞去重****能力**。在平台中需要将多种安全扫描工具相同安全问题去除掉，总体上可以按照N元组方式进行去重。例如：对于web接口类的安全问题，N元组设定成（接口名，问题类型，影响参数名）；对于静态扫描扫描类的问题，N元组设定成（文件名，代码行，类名，函数名等），通过以上方式可以较好的解决漏洞重复性。



**6）****漏洞风险等级重定义**。平台中使用到的安全扫描工具（开源/安全厂商）漏洞等级的情况和企业要求修复漏洞情况不同，不同企业对不同漏洞的敏感程度不同，企业不能完全根据厂商扫描工具漏洞等级判断是否紧急修复，而需根据实际漏洞危害情况判断不同危害程度，从而制定卡点修复，延期修复的漏洞黑名单列表，并在平台中对扫描出的漏洞风险做出级别重定义。



**7）定制化****安全****测试报告**。平台最终可以提供编排任务中使用到各个扫描工具安全测试报告，总体上分为内部报告和外部报告，内部报告的安全问题较为详细包括代码，payloa等，而外部报告将敏感内容进行脱敏。数据内容不限于不仅报告风险漏洞列表，还需包括工具多轮检测的过程发现的历史问题。



开发安全赋能平台通过ASOC重新定义安全测试工具使用方式，并通过定制剧本方式实现自动化处理工作流，并简化漏洞修复过程，关联和分析结果，并进行集中展示，便于开发人员理解安全问题，分类修复安全问题。平台充当应用开发和安全测试之间的管理和编排层。通过结合漏洞信息、资产信息、威胁情报综合计算出安全风险值，帮助企业识别出高风险漏洞进行优先修复，避免企业把精力浪费在低风险漏洞上。



**2****.5** **开发安全****度量指标**

**
**

管理学大师彼得·德鲁克（Peter Drucker）曾经说过，“你如果无法度量它，就无法管理它”（*If you can’t measure it, you can’t manage it*），因此，需要引入相关的度量指标对开发安全进行持续度量，客观、全面的体现开发安全赋能平台对当前的安全水位提升效果，横向对比与业界领先水平的差距，提出相关改进措施和制定重点工作，以便进一步提升企业整体信息安全防护和治理能力。



开发安全赋能平台对软件开发的安全架构设计评审、安全工具测试、人工渗透测试及上线前安全部署、漏洞与风险管理、应用系统/API资产安全管理与运营等各个阶段安全措施效果进行相关度量。



安全架构设计评审度量指标包括安全评估覆盖率、安全评估时效性、安全需求遵从率等。安全工具测试(SAST、SCA、DAST、IAST等工具)度量指标包括安全工具扫描覆盖率、安全工具扫描自动化率、安全工具扫描的漏洞误报率、安全工具扫描的漏洞发现率等。人工渗透测试度量指标包括人工渗透测试的漏洞发现率、人工渗透测试的漏洞漏测率、人工渗透测试的信息系统的覆盖率、人工渗透测试的时效性等。漏洞与风险管理度量指标包括漏洞修复时间、漏洞修复率、漏洞风险管控的有效性等。



开发安全赋能平台应该将度量指标可视化展示，提供管理者、开发者、领导者等视图，直观展示开发安全各项度量指标实时反馈。



**2****.6** **数据安全评估内嵌**

**
**

数据安全评估是指对重要数据、个人信息等数据资产的价值与权益、合规性、威胁、脆弱性、防护等进行分析和判断，以评估数据安全事件发生的概率和可能造成的损失，并采取相应的措施和建议。



面对外部的安全威胁以及监管的合规要求，机构需要通过开展数据安全评估，摸清数据资产情况、基础环境情况并对数据资产进行盘点；开展系统性、综合性、全面性的安全分析，发现数据资产的威胁性和脆弱性，从而更加准确识别安全风险；针对安全风险提出对应解决方案，并结合机构实际情况制定可执行的安全策略与安全目标，实现数据资产在安全的环境下进行有效利用。



通过开发安全赋能平台，将数据安全评估内嵌到应用系统研发流程，主要过程包括建立数据安全技术基线、递进式问卷调查、自动化威胁建模、分段式流程管理、安全验证及风险跟踪，实现数据安全评估基线化、工程化和技术化，实现隐私设计PbD，提升数据合规水平。



**2****.7** **应用系统攻击面基础数据管理**

**
**

在开发安全赋能平台登记应用相关攻击面资产，包括互联网服务、系统信息、网络信息、主机信息、制品信息、部署拓扑、开放服务、框架组件、API接口等，结合攻击面管理系统，通过互联网空间测绘、内部资产、CMDB等资源的关联汇总，完善应用维度的攻击面信息，嵌入应用生命周期各阶段，标准化、规范化整合攻击面信息，建立准确的应用资产台账，全面覆盖上线前后所有阶段，形成应用资产图谱，支撑风险关联及处置更加精准和迅捷。



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIVFZEsh9km6kfPH1a6yziaDHZFWdwlzgJib03KeOwjn47s4stsP6ZxHDQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



开发安全赋平台登记的相关资产信息作为重要的数据源，推动日常攻击面风险运营交叉验证和共同提升。在日常评估过程中，平台中的各种攻击面信息，作为重要的数据源，结合现有线上资产、CMDB等进行攻击面台账的完整性和准确性验证，而相关的攻击面信息（如API等）会在安全评估的时候提供给安全顾问，反向支撑相关信息填报的准确性和后续的渗透测试和各种安全扫描工作，多渠道脆弱性参考，推动风险分析及发现、互联网暴露面收敛等工作。



供应链管理亦嵌入到整个安全流程中，在采购阶段要求供应商签订信息安全补充条款并遵循供应链的相关条例，在安全评估过程中，需要提供供应链的相关内容，包括SBOM清单、供应商信息、应急预案、联系方式、版本信息、当前产品的已知或未修复漏洞等，对接公司供应商管理系统，结合供应链和攻击面管理系统维护相关数据，实现了供应链的准确台账，支持对供应链信息的分类和检索，及时发现并解决存在的供应链风险。




***\*03 开发安全赋能平台落地实践
\****



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rID0tO6huic8ZrWSrMhibaoRiaHsgGbSRBfjzXnOicAZZdcAibRgjsM42EUnA/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



笔者所在机构自主研发了三叉戟开发安全赋能平台，平台架构如下：



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIKUN6Bvm8SJeQeZRQeicQEojWPlGQCHaBszY7HmYx5eb5KiaoF0d6ohrg/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



研发过程的安全活动以项目安全评估的形式同步开展，并通过三叉戟开发安全赋能平台进行过程管控，项目组成员必须先通过一系列安全培训课程及考试取得安全BP资质认证后，才可登录平台进行安全评估活动。安全评估流程主要阶段包括调查问卷、轻量级威胁建模、安全测试、数据安全评估、生产验证及评估报告。



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIaYZ3WUfPoR2xf590X4vticexCEDLWMGFl8oZr5aoOr7SbPy7vWyibjdQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



调查问卷环节，安全BP需要提供系统的物理部署架构图以及相对应的用户场景、主机/容器资产、互联网资产、WEB-API文档、技术架构、获取方式以及敏感数据使用情况。三叉戟提供了数图联动能力来保障填报数据的一致性,安全专家可将典型的安全推荐的应用系统物理部署架构图设置为模板以供项目组快速选择，这样不仅可以给待建设系统提供安全架构方案推荐，也可以给已建设系统提供绘图方面的参考，不至于无从下手,安全专家可在平台上维护标准架构库。



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIhhl5EaHxCSClcCs9BfHBkoOppsGQFaVEAUia06Rk76FuACQeJWOJykQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



轻量级威胁建模环节，三叉戟会根据所填资料信息建模分析，自动化匹配安全需求库，输出系统需要完成的安全需求和安全活动。安全需求库承载了安全专家多年的领域知识和经验，每条需求在类别、来源、需求内容、参考实践、代码示例和禁止行为等多方面做了详细描述，在项目组进行系统建设时提供最详细的参考与指导。



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIMUnVA4iacgy05rhKNgN6VZt7f5Gz6lx95FzwibOiauBhVyDM6O3wHGLyQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIIwsStHVySvEbV7EqAvqOLhCYOz9TCrBStem0r3Z3fqdQJpHR9RBSibQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)























安全测试活动项会自动根据系统建模确定是否涉及。安全测试活动项包含源代码安全扫描、开源组件安全扫描、黑盒安全扫描、灰盒安全扫描、移动安全扫描、容器镜像安全扫描以及渗透测试。各种安全测试工具的漏洞在三叉戟中统一汇聚和管理。



数据安全评估环节，将数据分类分级、敏感数据识别、数据安全保护需求分析以及数据安全保护措施验证等数据安全评估活动嵌入安全评估流程，实现隐私融入设计方法PbD。



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIToE3cicyiaOePvPXKpqb8cECEaxA6jia7HwIv3SZbEglYmSibicTzcfgiaew/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



![图片](https://mmbiz.qpic.cn/mmbiz_png/kvCk9Nm6FzSWeDGicw3YJ5Iz10cp6ic5rIENYQ9HOId4MXhGkQib8datxRWHia4UpOehw7nNoObQsUSl6NicpCB2HMQ/640?from=appmsg&tp=webp&wxfrom=5&wx_lazy=1)



生产验证环节，三叉戟联动多源检查工具，包括HIDS、CMDB、漏洞扫描器及安全基线检测工具等，实现软件运行环境安全的自动检测与验证工作，包括主机上安全套件的安装情况、主机安全检测及web中间件安全基线检测等。

在评估报告环节，安全顾问在采取了所有可能的风险管理措施后，进行安全剩余风险评级，综合系统架构、运行环境、安全需求和各项检测活动结论，评定系统上线前最终剩余风险级别，形成应用系统安全评估报告。对于剩余风险中高以上的系统，原则上应进一步实施缓解措施，降低风险后再考虑上线。对于有特殊需求的系统，项目组在明确理解系统风险状态的前提下，签署并接受风险，进行审慎上线决策。





***\*04 总结
\****

三叉戟开发安全赋能平台，整合了本机构在SDL/DevSecOps领域多年积累的流程、方法、工具和知识库，已经全面落地并投入使用，每年为数百个应用系统提供全流程安全评估服务，为研发团队快速交付安全可靠的软件提供全面支撑，为公司数字化转型保驾护航。



通过自助化、基线化、流程化及自动化的平台赋能，提升了应用安全运营效能，降低了人力成本。通过轻量级威胁建模将安全需求落实到系统需求分析及架构设计中，通过设计保证安全，实现了应用系统内生安全，提升了应用系统自身主动免疫能力。安全左移，在软件开发早期阶段发现安全缺陷，从源头控制安全风险，极大的降低了漏洞修复成本。通过三叉戟，连接了研发、运维及安全，实现了安全职责共担，打破团队壁垒，提高团队协作效率。



三叉戟目前已经迭代发布多个版本，可以满足日常安全评估需求，但是，还存在一些不足，比如项目快速迭代的安全评估支撑能力不足、项目安全建设运营精细度不够等，因此，未来将持续基于自身用户体验反馈、DevSecOps发展趋势及开发安全领域新技术发展等，在三叉戟中落地更多应用场景，持续优化迭代开发安全赋能平台，不断提升软件全生命周期安全风险管理水平。

