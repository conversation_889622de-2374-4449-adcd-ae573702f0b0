<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>漏洞管理 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .main-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e4e7ed;
        }

        .search-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .vulnerability-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .vulnerability-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .vulnerability-card.critical {
            border-left: 4px solid #f56c6c;
        }

        .vulnerability-card.high {
            border-left: 4px solid #e6a23c;
        }

        .vulnerability-card.medium {
            border-left: 4px solid #409eff;
        }

        .vulnerability-card.low {
            border-left: 4px solid #67c23a;
        }

        .vuln-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .vuln-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }

        .vuln-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 12px;
        }

        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #606266;
        }

        .info-label {
            font-weight: bold;
            margin-right: 8px;
            min-width: 80px;
        }

        .vuln-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .vuln-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .timeline-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-title {
            font-weight: bold;
            color: #303133;
        }

        .timeline-time {
            font-size: 12px;
            color: #909399;
        }

        .timeline-content {
            color: #606266;
            font-size: 14px;
        }

        .fix-progress {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .fix-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .fix-title {
            font-weight: bold;
            color: #303133;
        }

        .fix-status {
            font-size: 12px;
        }

        .fix-description {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .cve-item {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .cve-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .cve-id {
            font-weight: bold;
            color: #d46b08;
        }

        .cve-score {
            font-size: 12px;
            color: #595959;
        }

        .cve-description {
            color: #595959;
            font-size: 14px;
        }

        .risk-matrix {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }

        .risk-cell {
            padding: 12px;
            text-align: center;
            border-radius: 6px;
            color: white;
            font-weight: bold;
        }

        .risk-critical {
            background-color: #722ed1;
        }

        .risk-high {
            background-color: #f5222d;
        }

        .risk-medium {
            background-color: #fa8c16;
        }

        .risk-low {
            background-color: #52c41a;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 漏洞管理
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="dashboard">
                            <el-icon><odometer /></el-icon>
                            <span>漏洞概览</span>
                        </el-menu-item>
                        <el-menu-item index="vulnerabilities">
                            <el-icon><warning /></el-icon>
                            <span>漏洞列表</span>
                        </el-menu-item>
                        <el-menu-item index="tracking">
                            <el-icon><view /></el-icon>
                            <span>修复跟踪</span>
                        </el-menu-item>
                        <el-menu-item index="cve">
                            <el-icon><document /></el-icon>
                            <span>CVE管理</span>
                        </el-menu-item>
                        <el-menu-item index="risk">
                            <el-icon><data-analysis /></el-icon>
                            <span>风险评估</span>
                        </el-menu-item>
                        <el-menu-item index="reports">
                            <el-icon><files /></el-icon>
                            <span>漏洞报告</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 主内容区域 -->
                <div class="content-area">
                    <!-- 漏洞概览 -->
                    <div v-show="currentPage === 'dashboard'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">漏洞管理概览</div>
                                <div class="page-subtitle">统一管理和跟踪所有安全漏洞的生命周期</div>
                            </div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #f56c6c;">{{ totalVulnerabilities }}</div>
                                <div class="stat-label">漏洞总数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #722ed1;">{{ criticalVulns }}</div>
                                <div class="stat-label">严重漏洞</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #e6a23c;">{{ highVulns }}</div>
                                <div class="stat-label">高危漏洞</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #67c23a;">{{ fixedVulns }}</div>
                                <div class="stat-label">已修复</div>
                            </div>
                        </div>

                        <div class="chart-grid">
                            <div class="chart-container">
                                <div class="section-title">漏洞趋势分析</div>
                                <canvas id="vulnTrendChart" width="400" height="200"></canvas>
                            </div>

                            <div class="chart-container">
                                <div class="section-title">漏洞分布统计</div>
                                <canvas id="vulnDistributionChart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="main-content">
                            <div class="section-title">风险矩阵</div>
                            <div class="risk-matrix">
                                <div class="risk-cell risk-critical">严重 ({{ criticalVulns }})</div>
                                <div class="risk-cell risk-high">高危 ({{ highVulns }})</div>
                                <div class="risk-cell risk-medium">中危 ({{ mediumVulns }})</div>
                                <div class="risk-cell risk-low">低危 ({{ lowVulns }})</div>
                            </div>

                            <div class="section-title">最新漏洞活动</div>
                            <div v-for="activity in recentActivities" :key="activity.id" class="timeline-item">
                                <div class="timeline-header">
                                    <div class="timeline-title">{{ activity.title }}</div>
                                    <div class="timeline-time">{{ activity.time }}</div>
                                </div>
                                <div class="timeline-content">{{ activity.content }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 漏洞列表 -->
                    <div v-show="currentPage === 'vulnerabilities'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">漏洞列表</div>
                                <div class="page-subtitle">查看和管理所有安全漏洞</div>
                            </div>
                            <el-button type="primary" @click="showCreateVulnDialog = true">
                                <el-icon><plus /></el-icon>
                                添加漏洞
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="toolbar">
                                <div class="search-bar">
                                    <el-input
                                        v-model="searchKeyword"
                                        placeholder="搜索漏洞标题或CVE编号"
                                        style="width: 300px;"
                                        clearable>
                                        <template #prefix>
                                            <el-icon><search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="severityFilter" placeholder="严重程度" style="width: 120px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="严重" value="critical"></el-option>
                                        <el-option label="高危" value="high"></el-option>
                                        <el-option label="中危" value="medium"></el-option>
                                        <el-option label="低危" value="low"></el-option>
                                    </el-select>
                                    <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="待修复" value="open"></el-option>
                                        <el-option label="修复中" value="fixing"></el-option>
                                        <el-option label="已修复" value="fixed"></el-option>
                                        <el-option label="已验证" value="verified"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button @click="batchFix">
                                        <el-icon><tools /></el-icon>
                                        批量修复
                                    </el-button>
                                    <el-button @click="exportVulns">
                                        <el-icon><download /></el-icon>
                                        导出
                                    </el-button>
                                </div>
                            </div>

                            <div v-for="vuln in filteredVulnerabilities" :key="vuln.id"
                                 :class="['vulnerability-card', vuln.severity.toLowerCase()]"
                                 @click="viewVulnDetail(vuln)">
                                <div class="vuln-header">
                                    <div class="vuln-title">{{ vuln.title }}</div>
                                    <div>
                                        <el-tag :type="getSeverityType(vuln.severity)" size="small">
                                            {{ vuln.severity }}
                                        </el-tag>
                                        <el-tag :type="getStatusType(vuln.status)" size="small" style="margin-left: 8px;">
                                            {{ vuln.status }}
                                        </el-tag>
                                    </div>
                                </div>

                                <div class="vuln-info">
                                    <div class="info-item">
                                        <span class="info-label">CVE编号:</span>
                                        <span>{{ vuln.cveId || '暂无' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">影响资产:</span>
                                        <span>{{ vuln.affectedAsset }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">发现时间:</span>
                                        <span>{{ vuln.discoveredAt }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">负责人:</span>
                                        <span>{{ vuln.assignee }}</span>
                                    </div>
                                </div>

                                <div class="vuln-description">{{ vuln.description }}</div>

                                <div class="vuln-actions" @click.stop>
                                    <el-button size="small" type="primary" @click="fixVulnerability(vuln)">修复</el-button>
                                    <el-button size="small" @click="assignVulnerability(vuln)">分配</el-button>
                                    <el-button size="small" @click="verifyVulnerability(vuln)">验证</el-button>
                                    <el-dropdown>
                                        <el-button size="small">
                                            更多<el-icon><arrow-down /></el-icon>
                                        </el-button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item @click="adjustSeverity(vuln)">调整等级</el-dropdown-item>
                                                <el-dropdown-item @click="addToWhitelist(vuln)">加入白名单</el-dropdown-item>
                                                <el-dropdown-item divided @click="deleteVulnerability(vuln)">删除漏洞</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 修复跟踪 -->
                    <div v-show="currentPage === 'tracking'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">修复跟踪</div>
                                <div class="page-subtitle">跟踪漏洞修复进度和状态</div>
                            </div>
                        </div>

                        <div class="main-content">
                            <div class="section-title">修复进度</div>
                            <div v-for="fix in fixProgress" :key="fix.id" class="fix-progress">
                                <div class="fix-header">
                                    <div class="fix-title">{{ fix.title }}</div>
                                    <el-tag :type="getFixStatusType(fix.status)" size="small">
                                        {{ fix.status }}
                                    </el-tag>
                                </div>
                                <div class="fix-description">{{ fix.description }}</div>
                                <div style="margin-bottom: 8px;">
                                    <span style="font-size: 12px; color: #909399;">进度: {{ fix.progress }}%</span>
                                </div>
                                <el-progress :percentage="fix.progress" :color="getProgressColor(fix.progress)"></el-progress>
                                <div style="margin-top: 8px; font-size: 12px; color: #909399;">
                                    负责人: {{ fix.assignee }} | 预计完成: {{ fix.dueDate }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CVE管理 -->
                    <div v-show="currentPage === 'cve'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">CVE管理</div>
                                <div class="page-subtitle">管理和跟踪CVE漏洞信息</div>
                            </div>
                            <el-button type="primary" @click="syncCVE">
                                <el-icon><refresh /></el-icon>
                                同步CVE
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div v-for="cve in cveList" :key="cve.id" class="cve-item">
                                <div class="cve-header">
                                    <div class="cve-id">{{ cve.cveId }}</div>
                                    <div class="cve-score">CVSS: {{ cve.cvssScore }}</div>
                                </div>
                                <div class="cve-description">{{ cve.description }}</div>
                                <div style="margin-top: 8px; font-size: 12px; color: #595959;">
                                    发布时间: {{ cve.publishedDate }} | 影响组件: {{ cve.affectedComponent }}
                                </div>
                                <div style="margin-top: 8px;">
                                    <el-button size="small" type="primary" @click="createVulnFromCVE(cve)">
                                        创建漏洞
                                    </el-button>
                                    <el-button size="small" @click="viewCVEDetail(cve)">
                                        查看详情
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 风险评估 -->
                    <div v-show="currentPage === 'risk'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">风险评估</div>
                                <div class="page-subtitle">评估和分析安全风险</div>
                            </div>
                            <el-button type="primary" @click="startRiskAssessment">
                                <el-icon><data-analysis /></el-icon>
                                开始评估
                            </el-button>
                        </div>

                        <div class="chart-container">
                            <div class="section-title">风险热力图</div>
                            <canvas id="riskHeatmapChart" width="800" height="400"></canvas>
                        </div>

                        <div class="main-content">
                            <div class="section-title">风险评估报告</div>
                            <el-table :data="riskAssessments" style="width: 100%">
                                <el-table-column prop="asset" label="资产" width="200"></el-table-column>
                                <el-table-column prop="riskLevel" label="风险等级" width="120">
                                    <template #default="scope">
                                        <el-tag :type="getRiskLevelType(scope.row.riskLevel)" size="small">
                                            {{ scope.row.riskLevel }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="vulnerabilityCount" label="漏洞数量" width="100"></el-table-column>
                                <el-table-column prop="riskScore" label="风险评分" width="100"></el-table-column>
                                <el-table-column prop="lastAssessment" label="最后评估" width="150"></el-table-column>
                                <el-table-column label="操作">
                                    <template #default="scope">
                                        <el-button size="small" @click="viewRiskDetail(scope.row)">详情</el-button>
                                        <el-button size="small" type="primary" @click="reassess(scope.row)">重新评估</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    activeMenu: 'dashboard',
                    currentPage: 'dashboard',
                    searchKeyword: '',
                    severityFilter: 'all',
                    statusFilter: 'all',
                    showCreateVulnDialog: false,

                    // 统计数据
                    totalVulnerabilities: 2847,
                    criticalVulns: 23,
                    highVulns: 156,
                    mediumVulns: 892,
                    lowVulns: 1776,
                    fixedVulns: 2683,

                    // 最新活动
                    recentActivities: [
                        {
                            id: 1,
                            title: '发现新的SQL注入漏洞',
                            content: '在证券交易系统的用户登录模块发现SQL注入漏洞，已分配给开发团队处理',
                            time: '2024-01-15 14:30'
                        },
                        {
                            id: 2,
                            title: 'Log4j漏洞修复完成',
                            content: 'CVE-2021-44228 Log4j远程代码执行漏洞已修复并通过验证',
                            time: '2024-01-15 11:20'
                        },
                        {
                            id: 3,
                            title: '批量漏洞扫描完成',
                            content: '对移动APP进行的安全扫描已完成，发现12个新漏洞',
                            time: '2024-01-14 16:45'
                        },
                        {
                            id: 4,
                            title: 'XSS漏洞验证通过',
                            content: '客户服务平台的XSS漏洞修复已通过安全验证',
                            time: '2024-01-14 09:15'
                        }
                    ],

                    // 漏洞列表
                    vulnerabilities: [
                        {
                            id: 1,
                            title: 'SQL注入漏洞 - 用户登录接口',
                            severity: '高危',
                            status: '待修复',
                            cveId: 'CVE-2024-0001',
                            affectedAsset: '证券交易系统',
                            discoveredAt: '2024-01-15',
                            assignee: '张三',
                            description: '在用户登录接口中发现SQL注入漏洞，攻击者可能通过构造恶意SQL语句获取数据库敏感信息。'
                        },
                        {
                            id: 2,
                            title: 'XSS跨站脚本攻击漏洞',
                            severity: '中危',
                            status: '修复中',
                            cveId: null,
                            affectedAsset: '客户服务平台',
                            discoveredAt: '2024-01-14',
                            assignee: '李四',
                            description: '在用户评论功能中发现存储型XSS漏洞，恶意脚本可能被永久存储并影响其他用户。'
                        },
                        {
                            id: 3,
                            title: 'Log4j远程代码执行漏洞',
                            severity: '严重',
                            status: '已修复',
                            cveId: 'CVE-2021-44228',
                            affectedAsset: '所有Java应用',
                            discoveredAt: '2024-01-10',
                            assignee: '王五',
                            description: 'Apache Log4j2组件存在远程代码执行漏洞，攻击者可通过JNDI注入执行任意代码。'
                        },
                        {
                            id: 4,
                            title: '弱密码策略漏洞',
                            severity: '低危',
                            status: '已验证',
                            cveId: null,
                            affectedAsset: '移动交易APP',
                            discoveredAt: '2024-01-12',
                            assignee: '赵六',
                            description: '系统密码策略过于宽松，存在暴力破解风险，建议加强密码复杂度要求。'
                        },
                        {
                            id: 5,
                            title: '敏感信息泄露',
                            severity: '中危',
                            status: '待修复',
                            cveId: null,
                            affectedAsset: 'API网关',
                            discoveredAt: '2024-01-13',
                            assignee: '钱七',
                            description: '错误页面中包含系统敏感信息，可能被攻击者利用进行进一步攻击。'
                        }
                    ],

                    // 修复进度
                    fixProgress: [
                        {
                            id: 1,
                            title: 'SQL注入漏洞修复',
                            description: '修复证券交易系统用户登录接口的SQL注入漏洞',
                            status: '进行中',
                            progress: 65,
                            assignee: '张三',
                            dueDate: '2024-01-20'
                        },
                        {
                            id: 2,
                            title: 'XSS漏洞修复',
                            description: '修复客户服务平台的存储型XSS漏洞',
                            status: '测试中',
                            progress: 85,
                            assignee: '李四',
                            dueDate: '2024-01-18'
                        },
                        {
                            id: 3,
                            title: '敏感信息泄露修复',
                            description: '修复API网关错误页面的敏感信息泄露问题',
                            status: '待开始',
                            progress: 0,
                            assignee: '钱七',
                            dueDate: '2024-01-25'
                        }
                    ],

                    // CVE列表
                    cveList: [
                        {
                            id: 1,
                            cveId: 'CVE-2024-0123',
                            description: 'Spring Framework远程代码执行漏洞',
                            cvssScore: '9.8',
                            publishedDate: '2024-01-10',
                            affectedComponent: 'Spring Framework 5.x'
                        },
                        {
                            id: 2,
                            cveId: 'CVE-2024-0124',
                            description: 'MySQL权限提升漏洞',
                            cvssScore: '7.2',
                            publishedDate: '2024-01-12',
                            affectedComponent: 'MySQL 8.0.x'
                        },
                        {
                            id: 3,
                            cveId: 'CVE-2024-0125',
                            description: 'Redis未授权访问漏洞',
                            cvssScore: '6.5',
                            publishedDate: '2024-01-14',
                            affectedComponent: 'Redis 6.x'
                        }
                    ],

                    // 风险评估
                    riskAssessments: [
                        {
                            asset: '证券交易系统',
                            riskLevel: '高风险',
                            vulnerabilityCount: 23,
                            riskScore: 8.5,
                            lastAssessment: '2024-01-15'
                        },
                        {
                            asset: '移动交易APP',
                            riskLevel: '中风险',
                            vulnerabilityCount: 12,
                            riskScore: 6.2,
                            lastAssessment: '2024-01-14'
                        },
                        {
                            asset: '客户服务平台',
                            riskLevel: '低风险',
                            vulnerabilityCount: 5,
                            riskScore: 3.8,
                            lastAssessment: '2024-01-13'
                        },
                        {
                            asset: 'API网关',
                            riskLevel: '中风险',
                            vulnerabilityCount: 8,
                            riskScore: 5.9,
                            lastAssessment: '2024-01-12'
                        }
                    ]
                };
            },

            computed: {
                filteredVulnerabilities() {
                    let filtered = this.vulnerabilities;

                    if (this.searchKeyword) {
                        filtered = filtered.filter(vuln =>
                            vuln.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                            (vuln.cveId && vuln.cveId.toLowerCase().includes(this.searchKeyword.toLowerCase()))
                        );
                    }

                    if (this.severityFilter !== 'all') {
                        filtered = filtered.filter(vuln => {
                            if (this.severityFilter === 'critical') return vuln.severity === '严重';
                            if (this.severityFilter === 'high') return vuln.severity === '高危';
                            if (this.severityFilter === 'medium') return vuln.severity === '中危';
                            if (this.severityFilter === 'low') return vuln.severity === '低危';
                            return true;
                        });
                    }

                    if (this.statusFilter !== 'all') {
                        filtered = filtered.filter(vuln => {
                            if (this.statusFilter === 'open') return vuln.status === '待修复';
                            if (this.statusFilter === 'fixing') return vuln.status === '修复中';
                            if (this.statusFilter === 'fixed') return vuln.status === '已修复';
                            if (this.statusFilter === 'verified') return vuln.status === '已验证';
                            return true;
                        });
                    }

                    return filtered;
                }
            },

            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },

                getSeverityType(severity) {
                    switch (severity) {
                        case '严重': return 'danger';
                        case '高危': return 'warning';
                        case '中危': return 'primary';
                        case '低危': return 'success';
                        default: return 'info';
                    }
                },

                getStatusType(status) {
                    switch (status) {
                        case '待修复': return 'danger';
                        case '修复中': return 'warning';
                        case '已修复': return 'success';
                        case '已验证': return 'info';
                        default: return 'info';
                    }
                },

                getFixStatusType(status) {
                    switch (status) {
                        case '进行中': return 'warning';
                        case '测试中': return 'primary';
                        case '已完成': return 'success';
                        case '待开始': return 'info';
                        default: return 'info';
                    }
                },

                getRiskLevelType(riskLevel) {
                    switch (riskLevel) {
                        case '高风险': return 'danger';
                        case '中风险': return 'warning';
                        case '低风险': return 'success';
                        default: return 'info';
                    }
                },

                getProgressColor(percentage) {
                    if (percentage < 30) return '#f56c6c';
                    if (percentage < 70) return '#e6a23c';
                    return '#67c23a';
                },

                viewVulnDetail(vuln) {
                    ElMessage.info(`查看漏洞"${vuln.title}"详情功能开发中...`);
                },

                fixVulnerability(vuln) {
                    ElMessageBox.confirm(
                        `确定要修复漏洞"${vuln.title}"吗？`,
                        '修复漏洞',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        vuln.status = '修复中';
                        ElMessage.success('漏洞已标记为修复中');
                    });
                },

                assignVulnerability(vuln) {
                    ElMessage.info(`分配漏洞"${vuln.title}"功能开发中...`);
                },

                verifyVulnerability(vuln) {
                    if (vuln.status !== '已修复') {
                        ElMessage.warning('只能验证已修复的漏洞');
                        return;
                    }

                    ElMessageBox.confirm(
                        `确定要验证漏洞"${vuln.title}"的修复结果吗？`,
                        '验证修复',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        vuln.status = '已验证';
                        ElMessage.success('漏洞修复验证完成');
                    });
                },

                adjustSeverity(vuln) {
                    ElMessage.info(`调整漏洞"${vuln.title}"等级功能开发中...`);
                },

                addToWhitelist(vuln) {
                    ElMessageBox.confirm(
                        `确定要将漏洞"${vuln.title}"加入白名单吗？`,
                        '加入白名单',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        ElMessage.success('漏洞已加入白名单');
                    });
                },

                deleteVulnerability(vuln) {
                    ElMessageBox.confirm(
                        `确定要删除漏洞"${vuln.title}"吗？此操作不可恢复。`,
                        '删除漏洞',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        const index = this.vulnerabilities.findIndex(v => v.id === vuln.id);
                        if (index > -1) {
                            this.vulnerabilities.splice(index, 1);
                            ElMessage.success('漏洞删除成功');
                        }
                    });
                },

                batchFix() {
                    ElMessage.info('批量修复功能开发中...');
                },

                exportVulns() {
                    ElMessage.success('漏洞列表导出成功');
                },

                syncCVE() {
                    ElMessage.success('CVE数据同步已启动');
                },

                createVulnFromCVE(cve) {
                    ElMessage.success(`基于${cve.cveId}创建漏洞成功`);
                },

                viewCVEDetail(cve) {
                    ElMessage.info(`查看${cve.cveId}详情功能开发中...`);
                },

                startRiskAssessment() {
                    ElMessage.success('风险评估已启动');
                },

                viewRiskDetail(risk) {
                    ElMessage.info(`查看${risk.asset}风险详情功能开发中...`);
                },

                reassess(risk) {
                    ElMessage.success(`${risk.asset}重新评估已启动`);
                },

                initCharts() {
                    // 漏洞趋势图
                    const trendCtx = document.getElementById('vulnTrendChart').getContext('2d');
                    new Chart(trendCtx, {
                        type: 'line',
                        data: {
                            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            datasets: [{
                                label: '新发现',
                                data: [45, 52, 38, 41, 35, 28],
                                borderColor: '#f56c6c',
                                backgroundColor: 'rgba(245, 108, 108, 0.1)',
                                tension: 0.4
                            }, {
                                label: '已修复',
                                data: [42, 48, 36, 39, 33, 26],
                                borderColor: '#67c23a',
                                backgroundColor: 'rgba(103, 194, 58, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });

                    // 漏洞分布图
                    const distCtx = document.getElementById('vulnDistributionChart').getContext('2d');
                    new Chart(distCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['严重', '高危', '中危', '低危'],
                            datasets: [{
                                data: [this.criticalVulns, this.highVulns, this.mediumVulns, this.lowVulns],
                                backgroundColor: [
                                    '#722ed1',
                                    '#f5222d',
                                    '#fa8c16',
                                    '#52c41a'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                }
                            }
                        }
                    });
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            },

            mounted() {
                this.$nextTick(() => {
                    this.initCharts();
                });
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
