<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用管理 - 开发安全赋能平台</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }

        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 240px;
            background: white;
            box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: #606266;
            font-size: 14px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #606266;
            font-size: 14px;
        }

        .main-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e4e7ed;
        }

        .search-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .app-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .app-card:hover {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
        }

        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .app-name {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }

        .app-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .info-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #606266;
        }

        .info-label {
            font-weight: bold;
            margin-right: 8px;
            min-width: 80px;
        }

        .app-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .security-status {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
        }

        .status-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #909399;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
        }

        .dialog-form {
            padding: 20px 0;
        }

        .form-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409eff;
        }

        .detail-tabs {
            margin-top: 20px;
        }

        .timeline-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-title {
            font-weight: bold;
            color: #303133;
        }

        .timeline-time {
            font-size: 12px;
            color: #909399;
        }

        .timeline-content {
            color: #606266;
            font-size: 14px;
        }

        .vulnerability-item {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .vuln-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .vuln-title {
            font-weight: bold;
            color: #cf1322;
        }

        .asset-item {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
        }

        .asset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .asset-name {
            font-weight: bold;
            color: #389e0d;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-content">
                    <div class="logo" @click="goHome">
                        <i class="logo-icon">🔱</i>
                        开发安全赋能平台 - 应用管理
                    </div>
                    <div>
                        <el-button type="primary" size="small" @click="goHome">返回首页</el-button>
                    </div>
                </div>
            </div>

            <div class="main-container">
                <!-- 侧边导航 -->
                <div class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        @select="handleMenuSelect"
                        style="border-right: none;">
                        <el-menu-item index="overview">
                            <el-icon><odometer /></el-icon>
                            <span>应用概览</span>
                        </el-menu-item>
                        <el-menu-item index="list">
                            <el-icon><document /></el-icon>
                            <span>应用列表</span>
                        </el-menu-item>
                        <el-menu-item index="lifecycle">
                            <el-icon><refresh /></el-icon>
                            <span>生命周期</span>
                        </el-menu-item>
                        <el-menu-item index="security">
                            <el-icon><lock /></el-icon>
                            <span>安全状态</span>
                        </el-menu-item>
                        <el-menu-item index="compliance">
                            <el-icon><check /></el-icon>
                            <span>合规检查</span>
                        </el-menu-item>
                    </el-menu>
                </div>

                <!-- 主内容区域 -->
                <div class="content-area">
                    <!-- 应用概览 -->
                    <div v-show="currentPage === 'overview'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">应用管理概览</div>
                                <div class="page-subtitle">统一管理应用生命周期，跟踪安全评估状态</div>
                            </div>
                        </div>

                        <div class="stats-cards">
                            <div class="stat-card">
                                <div class="stat-number" style="color: #409eff;">{{ totalApps }}</div>
                                <div class="stat-label">应用总数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #67c23a;">{{ activeApps }}</div>
                                <div class="stat-label">运行中应用</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #e6a23c;">{{ pendingApps }}</div>
                                <div class="stat-label">待评估应用</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" style="color: #f56c6c;">{{ riskApps }}</div>
                                <div class="stat-label">高风险应用</div>
                            </div>
                        </div>

                        <div class="main-content">
                            <div class="section-title">最近活动</div>
                            <div v-for="activity in recentActivities" :key="activity.id" class="timeline-item">
                                <div class="timeline-header">
                                    <div class="timeline-title">{{ activity.title }}</div>
                                    <div class="timeline-time">{{ activity.time }}</div>
                                </div>
                                <div class="timeline-content">{{ activity.content }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 应用列表 -->
                    <div v-show="currentPage === 'list'">
                        <div class="page-header">
                            <div>
                                <div class="page-title">应用列表</div>
                                <div class="page-subtitle">管理所有应用的基本信息和状态</div>
                            </div>
                            <el-button type="primary" @click="showCreateDialog = true">
                                <el-icon><plus /></el-icon>
                                新建应用
                            </el-button>
                        </div>

                        <div class="main-content">
                            <div class="toolbar">
                                <div class="search-bar">
                                    <el-input
                                        v-model="searchKeyword"
                                        placeholder="搜索应用名称或负责人"
                                        style="width: 300px;"
                                        clearable>
                                        <template #prefix>
                                            <el-icon><search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="运行中" value="running"></el-option>
                                        <el-option label="开发中" value="developing"></el-option>
                                        <el-option label="已下线" value="offline"></el-option>
                                    </el-select>
                                    <el-select v-model="typeFilter" placeholder="类型筛选" style="width: 150px;">
                                        <el-option label="全部" value="all"></el-option>
                                        <el-option label="Web应用" value="web"></el-option>
                                        <el-option label="移动应用" value="mobile"></el-option>
                                        <el-option label="API服务" value="api"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-button @click="exportApps">
                                        <el-icon><download /></el-icon>
                                        导出
                                    </el-button>
                                </div>
                            </div>

                            <div v-for="app in filteredApps" :key="app.id" class="app-card" @click="viewAppDetail(app)">
                                <div class="app-header">
                                    <div class="app-name">{{ app.name }}</div>
                                    <div>
                                        <el-tag :type="getStatusType(app.status)" size="small">
                                            {{ app.status }}
                                        </el-tag>
                                        <el-tag type="info" size="small" style="margin-left: 8px;">
                                            {{ app.type }}
                                        </el-tag>
                                    </div>
                                </div>

                                <div class="app-info">
                                    <div class="info-item">
                                        <span class="info-label">负责人:</span>
                                        <span>{{ app.owner }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">部门:</span>
                                        <span>{{ app.department }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">创建时间:</span>
                                        <span>{{ app.createdAt }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">最后更新:</span>
                                        <span>{{ app.updatedAt }}</span>
                                    </div>
                                </div>

                                <div class="security-status">
                                    <div class="status-item">
                                        <div class="status-dot" :style="{ backgroundColor: app.securityStatus.threat === '通过' ? '#67c23a' : '#f56c6c' }"></div>
                                        威胁建模: {{ app.securityStatus.threat }}
                                    </div>
                                    <div class="status-item">
                                        <div class="status-dot" :style="{ backgroundColor: app.securityStatus.scan === '通过' ? '#67c23a' : '#f56c6c' }"></div>
                                        安全扫描: {{ app.securityStatus.scan }}
                                    </div>
                                    <div class="status-item">
                                        <div class="status-dot" :style="{ backgroundColor: app.securityStatus.pentest === '通过' ? '#67c23a' : '#f56c6c' }"></div>
                                        渗透测试: {{ app.securityStatus.pentest }}
                                    </div>
                                    <div class="status-item">
                                        <div class="status-dot" :style="{ backgroundColor: app.securityStatus.compliance === '通过' ? '#67c23a' : '#f56c6c' }"></div>
                                        合规检查: {{ app.securityStatus.compliance }}
                                    </div>
                                </div>

                                <div class="app-actions" @click.stop>
                                    <el-button size="small" type="primary" @click="editApp(app)">编辑</el-button>
                                    <el-button size="small" @click="startThreatModeling(app)">威胁建模</el-button>
                                    <el-button size="small" @click="startSecurityScan(app)">安全扫描</el-button>
                                    <el-dropdown>
                                        <el-button size="small">
                                            更多<el-icon><arrow-down /></el-icon>
                                        </el-button>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item @click="viewAssets(app)">查看资产</el-dropdown-item>
                                                <el-dropdown-item @click="viewVulnerabilities(app)">查看漏洞</el-dropdown-item>
                                                <el-dropdown-item @click="generateReport(app)">生成报告</el-dropdown-item>
                                                <el-dropdown-item divided @click="deleteApp(app)">删除应用</el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建应用对话框 -->
        <el-dialog v-model="showCreateDialog" title="新建应用" width="600px">
            <div class="dialog-form">
                <el-form :model="newAppForm" label-width="100px">
                    <div class="form-section">
                        <div class="section-title">基本信息</div>
                        <el-form-item label="应用名称">
                            <el-input v-model="newAppForm.name" placeholder="请输入应用名称"></el-input>
                        </el-form-item>
                        <el-form-item label="应用类型">
                            <el-select v-model="newAppForm.type" placeholder="请选择应用类型">
                                <el-option label="Web应用" value="web"></el-option>
                                <el-option label="移动应用" value="mobile"></el-option>
                                <el-option label="API服务" value="api"></el-option>
                                <el-option label="微服务" value="microservice"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="负责人">
                            <el-input v-model="newAppForm.owner" placeholder="请输入负责人"></el-input>
                        </el-form-item>
                        <el-form-item label="所属部门">
                            <el-select v-model="newAppForm.department" placeholder="请选择部门">
                                <el-option label="技术部" value="tech"></el-option>
                                <el-option label="产品部" value="product"></el-option>
                                <el-option label="运营部" value="operation"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="应用描述">
                            <el-input type="textarea" v-model="newAppForm.description" placeholder="请输入应用描述"></el-input>
                        </el-form-item>
                    </div>

                    <div class="form-section">
                        <div class="section-title">技术信息</div>
                        <el-form-item label="技术栈">
                            <el-checkbox-group v-model="newAppForm.techStack">
                                <el-checkbox label="Java">Java</el-checkbox>
                                <el-checkbox label="Python">Python</el-checkbox>
                                <el-checkbox label="Node.js">Node.js</el-checkbox>
                                <el-checkbox label="React">React</el-checkbox>
                                <el-checkbox label="Vue.js">Vue.js</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="部署环境">
                            <el-radio-group v-model="newAppForm.environment">
                                <el-radio label="production">生产环境</el-radio>
                                <el-radio label="staging">测试环境</el-radio>
                                <el-radio label="development">开发环境</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <template #footer>
                <el-button @click="showCreateDialog = false">取消</el-button>
                <el-button type="primary" @click="createApp">创建</el-button>
            </template>
        </el-dialog>

        <!-- 应用详情对话框 -->
        <el-dialog v-model="showDetailDialog" title="应用详情" width="800px">
            <div v-if="selectedApp">
                <el-tabs v-model="activeDetailTab" class="detail-tabs">
                    <el-tab-pane label="基本信息" name="basic">
                        <el-descriptions :column="2" border>
                            <el-descriptions-item label="应用名称">{{ selectedApp.name }}</el-descriptions-item>
                            <el-descriptions-item label="应用类型">{{ selectedApp.type }}</el-descriptions-item>
                            <el-descriptions-item label="负责人">{{ selectedApp.owner }}</el-descriptions-item>
                            <el-descriptions-item label="所属部门">{{ selectedApp.department }}</el-descriptions-item>
                            <el-descriptions-item label="状态">
                                <el-tag :type="getStatusType(selectedApp.status)">{{ selectedApp.status }}</el-tag>
                            </el-descriptions-item>
                            <el-descriptions-item label="创建时间">{{ selectedApp.createdAt }}</el-descriptions-item>
                            <el-descriptions-item label="最后更新">{{ selectedApp.updatedAt }}</el-descriptions-item>
                            <el-descriptions-item label="应用描述" :span="2">{{ selectedApp.description }}</el-descriptions-item>
                        </el-descriptions>
                    </el-tab-pane>

                    <el-tab-pane label="安全状态" name="security">
                        <div style="margin-bottom: 20px;">
                            <div class="section-title">安全评估状态</div>
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-card>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; margin-bottom: 8px;" :style="{ color: selectedApp.securityStatus.threat === '通过' ? '#67c23a' : '#f56c6c' }">
                                                {{ selectedApp.securityStatus.threat === '通过' ? '✓' : '✗' }}
                                            </div>
                                            <div>威胁建模</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; margin-bottom: 8px;" :style="{ color: selectedApp.securityStatus.scan === '通过' ? '#67c23a' : '#f56c6c' }">
                                                {{ selectedApp.securityStatus.scan === '通过' ? '✓' : '✗' }}
                                            </div>
                                            <div>安全扫描</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; margin-bottom: 8px;" :style="{ color: selectedApp.securityStatus.pentest === '通过' ? '#67c23a' : '#f56c6c' }">
                                                {{ selectedApp.securityStatus.pentest === '通过' ? '✓' : '✗' }}
                                            </div>
                                            <div>渗透测试</div>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :span="6">
                                    <el-card>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; margin-bottom: 8px;" :style="{ color: selectedApp.securityStatus.compliance === '通过' ? '#67c23a' : '#f56c6c' }">
                                                {{ selectedApp.securityStatus.compliance === '通过' ? '✓' : '✗' }}
                                            </div>
                                            <div>合规检查</div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="section-title">最新漏洞</div>
                        <div v-for="vuln in selectedApp.vulnerabilities" :key="vuln.id" class="vulnerability-item">
                            <div class="vuln-header">
                                <div class="vuln-title">{{ vuln.title }}</div>
                                <el-tag :type="getSeverityType(vuln.severity)" size="small">{{ vuln.severity }}</el-tag>
                            </div>
                            <div style="color: #606266; font-size: 14px;">{{ vuln.description }}</div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="关联资产" name="assets">
                        <div class="section-title">应用资产</div>
                        <div v-for="asset in selectedApp.assets" :key="asset.id" class="asset-item">
                            <div class="asset-header">
                                <div class="asset-name">{{ asset.name }}</div>
                                <el-tag type="info" size="small">{{ asset.type }}</el-tag>
                            </div>
                            <div style="color: #606266; font-size: 14px;">{{ asset.description }}</div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="操作日志" name="logs">
                        <div class="section-title">操作历史</div>
                        <div v-for="log in selectedApp.logs" :key="log.id" class="timeline-item">
                            <div class="timeline-header">
                                <div class="timeline-title">{{ log.action }}</div>
                                <div class="timeline-time">{{ log.time }}</div>
                            </div>
                            <div class="timeline-content">{{ log.description }}</div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-dialog>
    </div>
    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    activeMenu: 'overview',
                    currentPage: 'overview',
                    searchKeyword: '',
                    statusFilter: 'all',
                    typeFilter: 'all',
                    showCreateDialog: false,
                    showDetailDialog: false,
                    selectedApp: null,
                    activeDetailTab: 'basic',

                    // 统计数据
                    totalApps: 156,
                    activeApps: 134,
                    pendingApps: 12,
                    riskApps: 8,

                    // 新建应用表单
                    newAppForm: {
                        name: '',
                        type: '',
                        owner: '',
                        department: '',
                        description: '',
                        techStack: [],
                        environment: 'production'
                    },

                    // 最近活动
                    recentActivities: [
                        {
                            id: 1,
                            title: '证券交易系统完成威胁建模',
                            content: '系统已完成威胁建模评估，识别出5个高风险威胁，生成了相应的安全需求',
                            time: '2024-01-15 14:30'
                        },
                        {
                            id: 2,
                            title: '移动APP安全扫描完成',
                            content: 'iOS和Android版本的安全扫描已完成，发现3个中危漏洞待修复',
                            time: '2024-01-15 11:20'
                        },
                        {
                            id: 3,
                            title: '新应用"客户服务平台"已创建',
                            content: '技术部创建了新的Web应用，负责人：张三，已开始安全评估流程',
                            time: '2024-01-14 16:45'
                        },
                        {
                            id: 4,
                            title: 'API网关渗透测试通过',
                            content: '第三方安全公司完成渗透测试，未发现高危漏洞，系统安全性良好',
                            time: '2024-01-14 09:15'
                        }
                    ],

                    // 应用列表
                    applications: [
                        {
                            id: 1,
                            name: '证券交易系统',
                            type: 'Web应用',
                            status: '运行中',
                            owner: '李四',
                            department: '技术部',
                            createdAt: '2023-06-15',
                            updatedAt: '2024-01-15',
                            description: '核心证券交易业务系统，支持股票、债券等金融产品交易',
                            securityStatus: {
                                threat: '通过',
                                scan: '通过',
                                pentest: '通过',
                                compliance: '通过'
                            },
                            vulnerabilities: [
                                {
                                    id: 1,
                                    title: 'SQL注入漏洞',
                                    severity: '高危',
                                    description: '登录接口存在SQL注入风险'
                                },
                                {
                                    id: 2,
                                    title: 'XSS跨站脚本',
                                    severity: '中危',
                                    description: '用户输入未正确过滤'
                                }
                            ],
                            assets: [
                                {
                                    id: 1,
                                    name: 'trade.example.com',
                                    type: '域名',
                                    description: '交易系统主域名'
                                },
                                {
                                    id: 2,
                                    name: '*************',
                                    type: 'IP地址',
                                    description: '生产服务器IP'
                                }
                            ],
                            logs: [
                                {
                                    id: 1,
                                    action: '完成威胁建模',
                                    description: '系统威胁建模评估已完成，生成安全需求基线',
                                    time: '2024-01-15 14:30'
                                },
                                {
                                    id: 2,
                                    action: '安全扫描',
                                    description: 'SAST和DAST扫描已完成，发现2个漏洞',
                                    time: '2024-01-14 10:20'
                                }
                            ]
                        },
                        {
                            id: 2,
                            name: '移动交易APP',
                            type: '移动应用',
                            status: '开发中',
                            owner: '王五',
                            department: '产品部',
                            createdAt: '2023-09-20',
                            updatedAt: '2024-01-14',
                            description: '移动端证券交易应用，支持iOS和Android平台',
                            securityStatus: {
                                threat: '通过',
                                scan: '待处理',
                                pentest: '未开始',
                                compliance: '通过'
                            },
                            vulnerabilities: [
                                {
                                    id: 3,
                                    title: '敏感信息泄露',
                                    severity: '中危',
                                    description: '日志中包含敏感用户信息'
                                }
                            ],
                            assets: [
                                {
                                    id: 3,
                                    name: 'mobile-api.example.com',
                                    type: 'API接口',
                                    description: '移动端API服务'
                                }
                            ],
                            logs: [
                                {
                                    id: 3,
                                    action: '创建应用',
                                    description: '移动交易APP项目已创建',
                                    time: '2023-09-20 09:00'
                                }
                            ]
                        },
                        {
                            id: 3,
                            name: '客户服务平台',
                            type: 'Web应用',
                            status: '运行中',
                            owner: '张三',
                            department: '技术部',
                            createdAt: '2023-12-01',
                            updatedAt: '2024-01-10',
                            description: '客户服务和支持系统，提供在线客服和工单管理',
                            securityStatus: {
                                threat: '通过',
                                scan: '通过',
                                pentest: '通过',
                                compliance: '待处理'
                            },
                            vulnerabilities: [],
                            assets: [
                                {
                                    id: 4,
                                    name: 'service.example.com',
                                    type: '域名',
                                    description: '客户服务平台域名'
                                }
                            ],
                            logs: [
                                {
                                    id: 4,
                                    action: '上线部署',
                                    description: '客户服务平台已成功部署到生产环境',
                                    time: '2024-01-10 15:30'
                                }
                            ]
                        }
                    ]
                };
            },

            computed: {
                filteredApps() {
                    let filtered = this.applications;

                    // 关键词搜索
                    if (this.searchKeyword) {
                        filtered = filtered.filter(app =>
                            app.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                            app.owner.toLowerCase().includes(this.searchKeyword.toLowerCase())
                        );
                    }

                    // 状态筛选
                    if (this.statusFilter !== 'all') {
                        filtered = filtered.filter(app => {
                            if (this.statusFilter === 'running') return app.status === '运行中';
                            if (this.statusFilter === 'developing') return app.status === '开发中';
                            if (this.statusFilter === 'offline') return app.status === '已下线';
                            return true;
                        });
                    }

                    // 类型筛选
                    if (this.typeFilter !== 'all') {
                        filtered = filtered.filter(app => {
                            if (this.typeFilter === 'web') return app.type === 'Web应用';
                            if (this.typeFilter === 'mobile') return app.type === '移动应用';
                            if (this.typeFilter === 'api') return app.type === 'API服务';
                            return true;
                        });
                    }

                    return filtered;
                }
            },

            methods: {
                handleMenuSelect(key) {
                    this.activeMenu = key;
                    this.currentPage = key;
                },

                getStatusType(status) {
                    switch (status) {
                        case '运行中': return 'success';
                        case '开发中': return 'warning';
                        case '已下线': return 'info';
                        default: return 'info';
                    }
                },

                getSeverityType(severity) {
                    switch (severity) {
                        case '高危': return 'danger';
                        case '中危': return 'warning';
                        case '低危': return 'info';
                        default: return 'info';
                    }
                },

                createApp() {
                    if (!this.newAppForm.name || !this.newAppForm.type || !this.newAppForm.owner) {
                        ElMessage.warning('请填写完整的基本信息');
                        return;
                    }

                    const newApp = {
                        id: this.applications.length + 1,
                        name: this.newAppForm.name,
                        type: this.newAppForm.type,
                        status: '开发中',
                        owner: this.newAppForm.owner,
                        department: this.newAppForm.department,
                        createdAt: new Date().toISOString().split('T')[0],
                        updatedAt: new Date().toISOString().split('T')[0],
                        description: this.newAppForm.description,
                        securityStatus: {
                            threat: '未开始',
                            scan: '未开始',
                            pentest: '未开始',
                            compliance: '未开始'
                        },
                        vulnerabilities: [],
                        assets: [],
                        logs: [{
                            id: 1,
                            action: '创建应用',
                            description: `应用"${this.newAppForm.name}"已创建`,
                            time: new Date().toLocaleString('zh-CN')
                        }]
                    };

                    this.applications.push(newApp);
                    this.showCreateDialog = false;
                    this.resetNewAppForm();
                    ElMessage.success('应用创建成功');
                },

                resetNewAppForm() {
                    this.newAppForm = {
                        name: '',
                        type: '',
                        owner: '',
                        department: '',
                        description: '',
                        techStack: [],
                        environment: 'production'
                    };
                },

                viewAppDetail(app) {
                    this.selectedApp = app;
                    this.showDetailDialog = true;
                    this.activeDetailTab = 'basic';
                },

                editApp(app) {
                    ElMessage.info('编辑应用功能开发中...');
                },

                startThreatModeling(app) {
                    ElMessageBox.confirm(
                        `是否为应用"${app.name}"启动威胁建模？`,
                        '启动威胁建模',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        ElMessage.success('威胁建模已启动，正在跳转...');
                        setTimeout(() => {
                            window.open('threat-modeling.html', '_blank');
                        }, 1000);
                    });
                },

                startSecurityScan(app) {
                    ElMessageBox.confirm(
                        `是否为应用"${app.name}"启动安全扫描？`,
                        '启动安全扫描',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'info'
                        }
                    ).then(() => {
                        ElMessage.success('安全扫描已启动，正在跳转...');
                        setTimeout(() => {
                            window.open('asoc-testing.html', '_blank');
                        }, 1000);
                    });
                },

                viewAssets(app) {
                    ElMessage.info('查看资产功能开发中...');
                },

                viewVulnerabilities(app) {
                    ElMessage.info('查看漏洞功能开发中...');
                },

                generateReport(app) {
                    ElMessage.success(`正在生成应用"${app.name}"的安全报告...`);
                },

                deleteApp(app) {
                    ElMessageBox.confirm(
                        `确定要删除应用"${app.name}"吗？此操作不可恢复。`,
                        '删除应用',
                        {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then(() => {
                        const index = this.applications.findIndex(a => a.id === app.id);
                        if (index > -1) {
                            this.applications.splice(index, 1);
                            ElMessage.success('应用删除成功');
                        }
                    });
                },

                exportApps() {
                    ElMessage.success('应用列表导出成功');
                },

                goHome() {
                    window.location.href = 'index.html';
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
